import MESSAGE from 'helpers/message';

const confirmWeightSchema = {
  type: 'object',
  additionalProperties: false,
  required: ['grossWeight', 'tareWeight'],
  properties: {
    grossWeight: {
      type: 'number',
      exclusiveMinimum: 0,
      maximum: 999999,
      errorMessage: {
        exclusiveMinimum: MESSAGE.MSG_LIMITS_GROSSWEIGHT_MIN_ERROR,
        maximum: MESSAGE.MSG_SAGE_NUM_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    tareWeight: {
      type: 'number',
      minimum: 0,
      maximum: 999999,
      exclusiveMaximum: {
        $data: '1/grossWeight',
      },
      errorMessage: {
        minimum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MIN_ERROR,
        maximum: MESSAGE.MSG_SAGE_NUM_ERROR,
        exclusiveMaximum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MAX_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
  },
};

export default confirmWeightSchema;
