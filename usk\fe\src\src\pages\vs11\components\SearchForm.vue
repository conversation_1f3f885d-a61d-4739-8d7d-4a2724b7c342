<template>
  <form>
    <div
      class="tw:grid tw:grid-rows-1 tw:grid-cols-1
      tw:tl:grid-cols-2 tw:gap-12 tw:tl:gap-8 tw:px-[.625rem]"
    >
      <!-- example 1 -->
      <div
        class="tw:font-normal tw:text-xs-design"
      >
        <div class="tw:mb-1 tw:tl:mb-4">仕入先(届出事業者名)</div>
        <BaseInput
          v-model.trim="searchFormProvideData.form.value.enterpriseName"
          input-class="tw:text-black tw:tl:leading-[2rem] tw:pb-1 tw:font-[400]
                tw:text-[1rem] tw:leading-[1.5rem] tw:tl:text-[2rem]"
          autocomplete="nope"
          outlined
          class="tw:mt-2"
          maxlength="256"
          :error="!!searchFormProvideData.errors.value.enterpriseName"
          :error-message="searchFormProvideData.errors.value.enterpriseName"
          no-error-icon
        />
      </div>

      <!-- example 2 -->
      <div
        class="tw:font-normal tw:text-xs-design"
      >
        <div class="tw:mb-1 tw:tl:mb-4">仕入先(事業者名)</div>
        <BaseInput
          v-model.trim="searchFormProvideData.form.value.supplier"
          input-class="tw:text-black tw:tl:leading-[2rem] tw:pb-1 tw:font-[400]
                tw:text-[1rem] tw:leading-[1.5rem] tw:tl:text-[2rem]"
          autocomplete="nope"
          outlined
          class="tw:mt-2"
          maxlength="256"
          :error="!!searchFormProvideData.errors.value.supplier"
          :error-message="searchFormProvideData.errors.value.supplier"
          no-error-icon
        />
      </div>

      <!-- arrivalDate -->
      <div
        class="tw:font-normal tw:text-xs-design tw:tl:col-span-1"
      >
        <form>
          <div class="tw:py-2">入荷日</div>
          <div
            class="tw:tl:flex tw:flex-cols tw:mb-1 tw:items-center tw:justify-between tw:gap-0"
          >
            <div class="tw:flex-1">
              <BaseDatePicker
                v-model="searchFormProvideData.form.value.startArrivalDate"
                :error="!!searchFormProvideData.errors.value.startArrivalDate"
                :error-message="
                  searchFormProvideData.errors.value.startArrivalDate
                "
                :class="{
                  'tw:mb-[3rem] tw:sm:mb-[0.5rem] tw:tl:mb-[1.3rem]':
                    !!searchFormProvideData.errors.value.startArrivalDate ||
                    !!searchFormProvideData.errors.value.endArrivalDate,
                }"
              />
            </div>
            <span
              class="tw:mb-[5px] tw:text-xl tw:w-8 tw:text-center tw:text-[#7E8093] tw:font-normal"
              :class="{
                'tw:mb-[4rem] tw:sm:mb-[1rem] tw:tl:mb-[1.5rem] tw:dt:mb-[2.3rem]':
                  !!searchFormProvideData.errors.value.startArrivalDate ||
                  !!searchFormProvideData.errors.value.endArrivalDate,
              }"
              >～</span
            >
            <div class="tw:flex-1">
              <BaseDatePicker
                v-model="searchFormProvideData.form.value.endArrivalDate"
                :error="!!searchFormProvideData.errors.value.endArrivalDate"
                :error-message="
                  searchFormProvideData.errors.value.endArrivalDate"
                :class="{
                  'tw:mb-[3rem] tw:sm:mb-[0.5rem] tw:tl:mb-[1.3rem]':
                    !!searchFormProvideData.errors.value.startArrivalDate ||
                    !!searchFormProvideData.errors.value.endArrivalDate,
                }"
              />
            </div>
          </div>
        </form>
      </div>
      <!-- example 3-->
      <div
        class="tw:font-normal tw:text-xs-design"
      >
        <div class="tw:mb-1 tw:tl:mb-4">届出番号</div>
        <BaseInput
          v-model.trim="searchFormProvideData.form.value.enterpriseCode"
          input-class="tw:text-black tw:pb-1 tw:font-[400] tw:text-xs-design"
          autocomplete="nope"
          outlined
          class="tw:mt-2"
          maxlength="256"
          :error="!!searchFormProvideData.errors.value.enterpriseCode"
          :error-message="searchFormProvideData.errors.value.enterpriseCode"
          no-error-icon
        />
      </div>

      <!-- license number -->
      <div
        class="tw:font-normal tw:text-xs-design"
      >
        <div class="tw:mb-1 tw:tl:mb-4">許可番号</div>
        <BaseInput
          v-model.trim="searchFormProvideData.form.value.licenseNumber"
          input-class="tw:text-[#333333] tw:text-xs-design tw:pb-1 tw:font-[400]"
          autocomplete="nope"
          outlined
          class="tw:mt-2"
          maxlength="256"
          :error="!!searchFormProvideData.errors.value.licenseNumber"
          :error-message="searchFormProvideData.errors.value.licenseNumber"
          no-error-icon
        />
      </div>
      <!-- note1 -->
      <div
        class="tw:font-normal tw:text-xs-design"
      >
        <div class="tw:mb-1 tw:tl:mb-4">備考１</div>
        <BaseInput
          v-model.trim="searchFormProvideData.form.value.note1"
          input-class="tw:text-[#333333] tw:text-xs-design tw:pb-1 tw:font-[400]"
          outlined
          autocomplete="nope"
          class="tw:mt-2"
          maxlength="256"
          :error="!!searchFormProvideData.errors.value.note1"
          :error-message="searchFormProvideData.errors.value.note1"
          no-error-icon
        />
      </div>
      <!-- note2 -->
      <div
        class="tw:font-normal tw:text-xs-design"
      >
        <div class="tw:mb-1 tw:tl:mb-4">備考２</div>
        <BaseInput
          v-model.trim="searchFormProvideData.form.value.note2"
          input-class="tw:text-[#333333] tw:text-xs-design tw:pb-1 tw:font-[400]"
          outlined
          autocomplete="nope"
          class="tw:mt-2"
          maxlength="256"
          :error="!!searchFormProvideData.errors.value.note2"
          :error-message="searchFormProvideData.errors.value.note2"
          no-error-icon
        />
      </div>
    </div>
  </form>
</template>

<script setup>
// #region import
import { ref, provide } from 'vue';
import BaseDatePicker from 'components/base/vs/BaseDatePicker.vue';
import BaseInput from 'components/base/vs/BaseInput.vue';
import {
  inject,
} from 'vue';
// #endregion

// #region state
const searchFormProvideData = inject('searchFormProvideData');
const listPartner = ref([]);

// #endregion

provide('basePartnerSelectProvideData', {
  listPartner,
});

</script>
