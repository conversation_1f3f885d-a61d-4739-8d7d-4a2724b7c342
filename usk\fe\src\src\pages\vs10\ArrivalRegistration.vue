<template>
  <div class="tw:h-full tw:flex tw:flex-col">
    <!-- #endregion -->
    <div class="tw:flex-1 tw:flex tw:flex-col tw:pb-[13rem] tw:tl:pb-[8rem]">
      <StepCustom v-model="step" class="tw:pb-3" />
      <q-tab-panels v-model="step" animated class="tw:text-[#333333] tw:flex-1">
        <q-tab-panel :name="STEP_ENUM.STEP_1" class="tw:p-4 tw:tl:py-0">
          <h2 :class="`tw:text-xs-design tw:tl:font-bold tw:py-3`">
            必要項目を入力して「次に進む」ボタンを押してください。
          </h2>
          <form
            class="tw:grid tw:grid-cols-1 tw:gap-y-5 tw:tl:grid-cols-2 tw:gap-x-12 tw:tl:gap-y-2"
          >
            <!-- 漁獲/荷口番号 -->
            <div>
              <BaseLabel label="漁獲/荷口番号" />
              <div class="tw:flex tw:text-xl-design tw:gap-2 tw:text-[#333333] tw:items-start">
                <div class="tw:shrink-0">{{ maskCodeString(form.code) }}</div>
              </div>
            </div>
            <!-- 仕入先 -->
            <div>
              <BaseLabel label="仕入先" />
              <span :class="`tw:block tw:text-l-design tw:py-1 tw:truncate`">
                {{ supplierInfo?.name || '' }}
              </span>
            </div>
            <!-- 入荷日 -->
            <div class="tw:mt-5 tw:tl:mt-0">
              <BaseLabel label="入荷日" isRequired />
              <BaseDatePicker
                v-model="form.date"
                class="tw:flex-1"
                input-class="tw:text-xl-design tw:text-[#333333]"
                :class="{
                  'tw:sm:mb-[0.5rem] tw:tl:mb-[1.3rem]': !!errorStep1.date,
                }"
                :error="!!errorStep1.date"
                :error-message="errorStep1.date"
              />
            </div>
            <!-- element offset -->
            <div></div>
            <div class="tw:mt-5" v-if="displayShipmentWeight">
              <BaseLabel label="出荷量" />
              <div class="tw:flex tw:text-xl-design tw:gap-2 tw:text-[#333333] tw:items-start">
                <div class="tw:shrink-0">{{ form.shipping_net_weight }}g</div>
              </div>
            </div>
          </form>
        </q-tab-panel>
        <q-tab-panel :name="STEP_ENUM.STEP_2" class="tw:p-4 tw:tl:py-0">
          <h2 :class="`tw:text-xs-design tw:tl:font-bold tw:py-3`">
            必要項目を入力して「次に進む」ボタンを押してください。
          </h2>
          <form class="tw:grid tw:grid-cols-1 tw:tl:grid-cols-2 tw:gap-x-12 tw:gap-y-2">
            <div class="tw:mb-8 tw:tl:mb-0">
              <!-- 全体重量 -->
              <BaseLabel label="全体重量" isRequired />
              <BaseInput
                type="text"
                maxlength="12"
                inputmode="numeric"
                autocomplete="nope"
                :model-value="form.grossWeight"
                @update:model-value="handleInputGrossWeight"
                :mask="{
                  mask: Number,
                  min: 0,
                  thousandsSeparator: ',',
                  scale: 2,
                  max: 9999999.99,
                  radix: '.',
                  autofix: true,
                  lazy: false,
                }"
                outlined
                class="tw:!h-[7rem]"
                input-class="tw:text-right tw:text-xl-bold-design"
                :error="!!errorStep2.grossWeight"
                :error-message="errorStep2.grossWeight"
                suffix="g"
                :is-clearable="!!form.grossWeight"
              >
              </BaseInput>
            </div>
            <div class="tw:flex tw:items-end tw:relative tw:mb-8 tw:tl:mb-0 tw:mt-5 tw:tl:mt-0">
              <!-- subtraction sign sm -->
              <div class="tw:h-1 tw:bg-[#7E8093] tw:px-4 tw:mb-8 tw:mx-5 tw:tl:hidden" />
              <div class="tw:flex-1">
                <!-- subtraction sign tl -->
                <div
                  class="tw:h-1 tw:bg-[#7E8093] tw:px-3 tw:mb-8 tw:mx-5 tw:hidden tw:tl:block tw:absolute tw:top-[4.8rem] tw:-left-[3.5rem]"
                />
                <!-- 風袋 -->
                <BaseLabel label="風袋" />
                <BaseInput
                  type="text"
                  maxlength="12"
                  inputmode="numeric"
                  autocomplete="nope"
                  :model-value="form.tareWeight"
                  @update:model-value="handleInputTareWeight"
                  :mask="{
                    mask: Number,
                    min: 0,
                    max: 9999999.99,
                    thousandsSeparator: ',',
                    scale: 2,
                    radix: '.',
                    autofix: true,
                    lazy: false,
                  }"
                  outlined
                  input-class="tw:text-right tw:text-xl-bold-design"
                  :error="!!errorStep2.tareWeight && !errorStep2.grossWeight"
                  :error-message="errorStep2.tareWeight"
                  suffix="g"
                  :is-clearable="!!form.tareWeight"
                >
                </BaseInput>
              </div>
            </div>
            <!-- Divider -->
            <div
              class="tw:tl:col-span-2 tw:h-[1px] tw:bg-[#CBCBCB] tw:mt-2"
              :class="{
                'tw:tl:mt-14': errorStep2.tareWeight || errorStep2.grossWeight,
                'tw:mt-14': errorStep2.tareWeight,
              }"
            />
            <div class="tw:tl:col-span-2 tw:flex tw:justify-between tw:items-center tw:gap-4">
              <!-- 入荷量 -->
              <div class="tw:text-m-design tw:flex tw:gap-0.5 tw:items-center tw:mb-1 tw:xl:mb-0">
                入荷量
              </div>
              <div class="tw:font-bold">
                <span class="tw:text-xl-bold-design">
                  {{ form.netWeight }}
                </span>
                <span class="tw:text-[2.375rem]"> g </span>
              </div>
            </div>
          </form>
        </q-tab-panel>
      </q-tab-panels>
    </div>
    <q-footer
      elevated
      class="tw:bg-white tw:p-4 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)]
      tw:w-full tw:tl:justify-between tw:flex tw:justify-center tw:mt-4
      tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        v-if="step === STEP_ENUM.STEP_1"
        outline
        class="tw:rounded-[40px] tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]"
        label="入荷登録方法選択に戻る"
        @click="handleBack"
      />
      <BaseButton
        v-else
        outline
        class="tw:rounded-[40px] tw:tl:w-[16.5rem] tw:bg-white tw:text-blue-3
        tw:text-m-design tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]"
        label="前に戻る"
        @click="step = STEP_ENUM.STEP_1"
      />
      <BaseButton
        outline
        class="tw:rounded-[40px] tw:bg-blue-3 tw:text-white tw:text-m-design
        tw:tl:w-[16.5rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]"
        label="次に進む"
        @click="handleClickNext"
      />
    </q-footer>
  </div>
  <PopupReasonDifference
    v-model="isShowPopupReason"
    :shippingWeight="
      displayShipmentWeight ? doParseFloatNumber(form.shipping_net_weight) : undefined
    "
    :arrivalWeight="doParseFloatNumber(form.netWeight)"
    @onClickCancel="handleCancelPopupReason"
    @onClickSubmit="handleConfirmPopupReason"
  />
</template>
<script setup>
import dayjs from 'boot/dayjs';
import BaseButton from 'components/base/vs/BaseButton.vue';
import BaseDatePicker from 'components/base/vs/BaseDatePicker.vue';
import BaseInput from 'components/base/vs/BaseInput.vue';
import useValidate from 'composables/validate';
import { storeToRefs } from 'pinia';
import StepCustom from 'src/components/StepCustom.vue';
import BaseLabel from 'src/components/base/vs/BaseLabel.vue';
import { doParseFloatNumber, FORMAT_NUMBER, isNumeric, maskCodeString } from 'src/helpers/common';
import { ADMIN_SYSTEM_SETTING_KEYS_ENUM, STEP_ENUM } from 'src/helpers/constants';
import {
  registerArrivalStep1,
  registerArrivalStep2,
} from 'src/schemas/arrival/registerArrival.schema';
import systemSettingsAdminService from 'src/shared/services/admin/systemSettings.admin.service';
import arrivalService from 'src/shared/services/arrival.service';
import { useConfirmFormStore } from 'src/stores/confirm-form-store';
import { useAppStore } from 'stores/app-store';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import PopupReasonDifference from './components/PopupReasonDifference.vue';

// #region state
const router = useRouter();
const { settingUser, previousRoute } = storeToRefs(useAppStore());
const { setConfirmData, getConfirmData } = useConfirmFormStore();
const { validateData: validateStep1, errors: errorStep1 } = useValidate();
const { validateData: validateStep2, errors: errorStep2 } = useValidate();
const supplierInfo = ref(null);
const displayShipmentWeight = ref(false);
const isShowPopupReason = ref(false);
const systemSetting = ref(null);
const unitPerGram = ref(settingUser.value.unit_per_gram ?? 0);

const form = ref({
  code: '',
  supplier: '',
  date: dayjs().format('YYYY/MM/DD'),
  shipping_net_weight: '',
  grossWeight: '',
  tareWeight: '',
  netWeight: '000,000.00',
  quantity: '',
  typeDiff: '',
  reasonDiff: '',
});
const step = ref(STEP_ENUM.STEP_1);
// #endregion

// #region action
const handleInputGrossWeight = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const grossWeightNum = doParseFloatNumber(newValue || 0);
  const tareWeightNum = doParseFloatNumber(form.value.tareWeight || 0);
  const netWeightNum = grossWeightNum - tareWeightNum;
  form.value.grossWeight = newValue;
  form.value.netWeight = netWeightNum >= 0 ? FORMAT_NUMBER(netWeightNum, 2) : undefined;
  form.value.quantity =
    netWeightNum >= 0
      ? FORMAT_NUMBER(
          Math.ceil((netWeightNum / unitPerGram.value).toFixed(3))
        )
      : undefined;
};

const handleInputTareWeight = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const grossWeightNum = doParseFloatNumber(form.value.grossWeight || 0);
  const tareWeightNum = doParseFloatNumber(newValue || 0);
  const netWeightNum = grossWeightNum - tareWeightNum;
  form.value.tareWeight = newValue;
  form.value.netWeight = netWeightNum >= 0 ? FORMAT_NUMBER(netWeightNum, 2) : undefined;
  form.value.quantity =
    netWeightNum >= 0
      ? FORMAT_NUMBER(
          Math.ceil((netWeightNum / unitPerGram.value).toFixed(3))
        )
      : undefined;
};

const handleBack = () => {
  router.back();
};

const handleClickNext = () => {
  if (step.value === STEP_ENUM.STEP_1) {
    const validate = validationStep1();
    if (!validate) {
      return;
    }
    step.value = STEP_ENUM.STEP_2;
  } else {
    const validate = validationStep2();
    if (!validate) {
      return;
    }

    const shippingWeight = doParseFloatNumber(form.value.shipping_net_weight);
    const arrivalWeight = doParseFloatNumber(form.value.netWeight);
    if (
      (Math.abs(shippingWeight - arrivalWeight) / shippingWeight) * 100 >
      systemSetting.value[ADMIN_SYSTEM_SETTING_KEYS_ENUM.WEIGHT_ALERT_THRESHOLD]
    ) {
      isShowPopupReason.value = true;
    } else {
      form.value.typeDiff = '';
      form.value.reasonDiff = '';
      setConfirmData({
        ...form.value,
        supplierInfo: supplierInfo.value,
        displayShipmentWeight: displayShipmentWeight.value,
      });
      router.push({
        name: 'registrationConfirmation',
        params: {
          qrCode: router.currentRoute.value.params.qrCode,
        },
      });
    }
  }
};

const handleCancelPopupReason = () => {
  isShowPopupReason.value = false;
};

const handleConfirmPopupReason = payload => {
  form.value.typeDiff = payload.typeDiff;
  form.value.reasonDiff = payload.reasonDiff;
  handleCancelPopupReason();
  setConfirmData({
    ...form.value,
    supplierInfo: supplierInfo.value,
    displayShipmentWeight: displayShipmentWeight.value,
  });
  router.push({
    name: 'registrationConfirmation',
    params: {
      qrCode: router.currentRoute.value.params.qrCode,
    },
  });
};
// #endregion

// #region helpers
const validationStep1 = () => {
  const payload = {
    date: form.value.date,
  };
  return validateStep1(registerArrivalStep1, payload);
};

const validationStep2 = () => {
  const payload = {
    grossWeight: form.value.grossWeight ? doParseFloatNumber(form.value.grossWeight) : '',
    tareWeight: form.value.tareWeight ? doParseFloatNumber(form.value.tareWeight) : 0,
  };
  return validateStep2(registerArrivalStep2, payload);
};
// #endregion

// #region lifecycle hooks
onMounted(async () => {
  // get qr code
  const qrCode = router.currentRoute.value.params?.qrCode;
  if (!qrCode) {
    router.push({
      name: 'home',
    });
  }

  const systemSettingResponse = await systemSettingsAdminService.getSystemSettingsForNormalUser();
  if (systemSettingResponse) {
    systemSetting.value = systemSettingResponse.payload;
  }

  // get data from confirm form store
  const shipmentStoreData = getConfirmData();
  // if shipmentStoreData is not empty, set form values
  if (shipmentStoreData && previousRoute.value.name === 'registrationConfirmation') {
    const {
      supplierInfo: supplierInfoStore,
      displayShipmentWeight: displayShipmentWeightStore,
      ...rest
    } = shipmentStoreData;
    // pass rest of the data to form
    form.value = {
      ...form.value,
      ...rest,
    };
    // set supplier
    supplierInfo.value = supplierInfoStore || {};
    displayShipmentWeight.value = displayShipmentWeightStore;
  } else {
    const shippingDetailResponse = await arrivalService.getDetailQrArrival({ qrCode });
    if (shippingDetailResponse.code === 0) {
      const payload = shippingDetailResponse.payload;
      // pass information to form
      form.value.code = payload.code;
      form.value.shipping_net_weight = FORMAT_NUMBER(payload.shipping_net_weight);
      form.value.supplier = payload.starting_user_id;
      supplierInfo.value = payload.starting_user;
      displayShipmentWeight.value = payload.setting.display_shipment_weight;
    } else {
      toast.error(shippingDetailResponse.message);
      await router.push({ name: 'home' });
    }
  }
});
// #endregion
</script>
<style scoped>
:deep(.q-field--outlined .q-field__control) {
  padding-right: 0;
}

:deep(.q-field__control-container .q-field__suffix) {
  margin-right: 1rem !important;
  font-size: 2rem;
  line-height: 1.5;
}

:deep(.q-radio__inner--truthy){
  color: #004AB9;
}

:deep(.q-radio__inner--falsy){
  color: #CBCBCB;
}
</style>
