<template>
  <OutboundShipmentPDF />
  <ExportShipmentPDF />
  <div class="tw:bg-white tw:p-4 tw:flex tw:flex-col tw:h-full tw:pb-[20rem] tw:tl:pb-[8rem]">
    <div class="tw:text-xs-design tw:font-bold tw:text-[#333333]">
      入力内容を確認して「登録する」ボタンを押してください。
    </div>
    <div class="tw:mt-4 tw:grid tw:grid-cols-1 tw:tl:grid-cols-2 tw:gap-2">
      <div>
        <div class="tw:font-normal tw:text-s-design tw:relative">
          漁獲/荷口番号
          <q-badge
            class="tw:text-white tw:bg-[#E80F00] tw:p-1 tw:rounded font-xxs-design tw:absolute tw:bottom-[0.4rem] tw:dt:bottom-[0.6rem] tw:ml-2"
          >
            必須
          </q-badge>
        </div>
        <div class="tw:text-l-design tw:font-bold">
          {{ maskCodeString(`${displayData.mainCode}${displayData.subCode}`) }}
        </div>
      </div>
      <div>
        <div class="tw:font-normal tw:text-s-design tw:relative">
          出荷先
          <q-badge
            class="tw:text-white tw:bg-[#E80F00] tw:p-1 tw:rounded font-xxs-design tw:absolute tw:bottom-[0.4rem] tw:dt:bottom-[0.6rem] tw:ml-2"
          >
            必須
          </q-badge>
        </div>
        <div class="tw:text-l-design tw:font-bold tw:truncate">
          {{ displayData.shipperSelected?.label }}
        </div>
      </div>
    </div>
    <div class="tw:mt-4">
      <div class="tw:font-normal tw:text-s-design tw:relative">
        出荷日
        <q-badge
          class="tw:text-white tw:bg-[#E80F00] tw:p-1 tw:rounded font-xxs-design tw:absolute tw:bottom-[0.4rem] tw:dt:bottom-[0.6rem] tw:ml-2"
        >
          必須
        </q-badge>
      </div>
      <div class="tw:text-l-design tw:font-bold">{{ FORMAT_DATE(displayData.date) }}</div>
    </div>
    <div class="tw:mt-4">
      <div class="tw:font-normal tw:text-s-design tw:relative">
        出荷量
        <q-badge
          class="tw:text-white tw:bg-[#E80F00] tw:p-1 tw:rounded font-xxs-design tw:absolute tw:bottom-[0.4rem] tw:dt:bottom-[0.6rem] tw:ml-2"
        >
          必須
        </q-badge>
      </div>
      <div class="tw:flex tw:gap-5">
        <div class="tw:text-l-design tw:font-bold">
          {{
            confirmWeightInfo
              ? FORMAT_NUMBER(confirmWeightInfo.grossWeight - confirmWeightInfo.tareWeight, 2)
              : 0
          }}
          g
        </div>
        <BaseButton
          outline
          class="tw:rounded-[40px]"
          :class="`tw:bg-white tw:text-[#004AB9] tw:text-xs-design tw:tl:font-bold
          tw:tl:w-[18.9rem] tw:tl:max-h-[4rem] tw:tl:min-h-[4rem] tw:h-[4rem]`"
          label="再計量をする"
          @click.prevent="handleConfirmTotalWeight()"
        />
      </div>
      <q-card class="tw:border-[#004AB9] tw:border-2 tw:mt-4" bordered>
        <q-expansion-item
          expand-separator
          class="w-full"
          header-class="tw:text-m-design tw:text-[#004AB9] tw:font-bold"
          expand-icon-class="tw:text-[3rem] tw:text-[#004AB9] tw:pr-0!"
        >
          <template #header>
            <div class="row items-center tw:flex-1">
              <q-img :src="svg" class="tw:w-[2.25rem]" />
              <span class="tw:ml-2 tw:text-m-design tw:mt-[0.125rem] tw:dt:mt-[0.375rem]"
                >出荷内訳</span
              >
            </div>
          </template>

          <!-- table -->
          <q-table
            grid
            hide-bottom
            card-container-class="tw:flex-col tw:gap-3 tw:m-4"
            card-class="tw:w-full"
            row-key="id"
            :rows="inventorySelectedList"
          >
            <template v-slot:item="props">
              <div class="tw:border-2 tw:border-[#004AB9] tw:rounded-[0.5rem]">
                <q-card flat bordered>
                  <q-card-section
                    class="tw:cursor-pointer tw:text-[#333333] tw:text-m-design tw:p-3 tw:flex tw:tl:flex-row tw:justify-between tw:flex-col"
                  >
                    <div class="tw:flex tw:items-center">
                      <div>
                        <div class="tw:font-bold tw:truncate">{{ props.row.groupName }}</div>
                        <div class="tw:flex-col tw:flex tw:tl:flex-row tw:gap-4">
                          <div class="tw:text-m-design">
                            <p class="tw:m-0">
                              最新の入荷日：{{ FORMAT_DATE(props.row.latestArrivalDate) }}
                            </p>
                          </div>
                          <div>
                            <span
                              >総在庫量：{{
                                FORMAT_NUMBER(props.row.netWeightInventory, 2) || 0
                              }}
                              g</span
                            >
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="tw:font-bold tw:text-right">
                      {{ mapNetWeightInventoryToDisplay(props.row) }}
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </template>
          </q-table>
        </q-expansion-item>
      </q-card>
    </div>
    <!-- footer -->
    <q-footer
      elevated
      class="tw:bg-white tw:p-3 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
      tw:items-center tw:flex tw:justify-center tw:tl:justify-between tw:min-h-[91px] tw:tl:h-[6.5rem]
      tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-[#004AB9]  tw:text-m-design tw:tl:font-bold
        tw:tl:w-[23.5rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
        tw:w-full`"
        label="入力内容を修正する"
        @click.prevent="router.back()"
      />
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-[#004AB9] tw:text-white tw:text-m-design tw:tl:font-bold
        tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
        tw:w-full`"
        label="登録する"
        @click.prevent="handleClickSubmit"
      />
    </q-footer>
    <PopupConfirmTotalWeight
      v-model="isShowPopupConfirmTotalWeight"
      :confirmWeightInfo="confirmWeightInfo"
      @onClickCancel="handleCancelPopupConfirmTotalWeight"
      @onClickSubmit="handleConfirmPopupConfirmTotalWeight"
    />
    <PopupDifferenceMeasurement
      v-model="isShowPopupDifferenceMeasurement"
      @onClickCancel="handleCancelPopupDifferenceMeasurement"
      @onClickSubmit="handleConfirmPopupDifferenceMeasurement"
    />
    <PopupConfirmText />
  </div>
</template>

<script setup>
// #region import
import UnionShipment from 'assets/UnionShipment.svg';
import BaseButton from 'components/base/vs/BaseButton.vue';
import PopupConfirmText from 'components/PopupConfirmText.vue';
import ExportShipmentPDF from 'src/components/pdf/ExportShipmentPDF.vue';
import OutboundShipmentPDF from 'components/pdf/OutboundShipmentPDF.vue';
import {
  FORMAT_DATE,
  FORMAT_NUMBER,
  CHECK_ROLE,
  FORMAT_DATE_TIME_CSV,
  maskCodeString,
} from 'helpers/common';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'src/stores/app-store';
import { useConfirmFormStore } from 'src/stores/confirm-form-store';
import { computed, nextTick, onMounted, provide, ref } from 'vue';
import { useRouter } from 'vue-router';
import {
  ADMIN_SYSTEM_SETTING_KEYS_ENUM,
  ENTERPRISE_TYPE_ENUM,
  INVENTORY_TYPE_ENUM,
  REPORT_TYPE_ENUM,
  ROLES_ENUM,
  STAFF_TYPE_ENUM,
} from 'src/helpers/constants';
import systemSettingsAdminService from 'src/shared/services/admin/systemSettings.admin.service';
import outboundShipmentService from 'src/shared/services/outboundShipment.service';
import toast from 'src/shared/utilities/toast';
import MESSAGE from 'src/helpers/message';
import html2pdf from 'html2pdf.js';
import shippingService from 'src/shared/services/shipping.service';
import { makeShippingInfoXML } from 'src/boot/print';
import dayjs from 'boot/dayjs';

import PopupDifferenceMeasurement from './components/PopupDifferenceMeasurement.vue';
import PopupConfirmTotalWeight from './components/PopupConfirmTotalWeight.vue';
// #endregion import

const router = useRouter();
const svg = ref(UnionShipment);
const { getConfirmData } = useConfirmFormStore();
const { previousRoute, settingUser } = storeToRefs(useAppStore());
const displayData = ref({});
const isShowPopupConfirmTotalWeight = ref(false);
const isShowPopupDifferenceMeasurement = ref(false);
const isShowPopupDownloadPDF = ref(false);
const weightAlertThreshold = ref(0);
const reasonInfo = ref(null);
const confirmWeightInfo = ref(null);
const dataExport = ref({});
const exportShipmentPDFProvideData = ref({
  rootOrigins: [],
  distributionOrigins: [],
  catchingOrigins: [],
});

// #region helper
const mapNetWeightInventoryToDisplay = item => {
  if (item.selectedInfo.checked && item.selectedInfo.netWeight < item.netWeightInventory) {
    return `${FORMAT_NUMBER(item.selectedInfo.netWeight, 2)}g`;
  }
  return '在庫全て';
};

const saveOutboundShipment = () => {
  const ingredient = displayData.value.inventorySelectedList.map(item => ({
    shipping_inventory_id: item.id,
    gross_weight: item.selectedInfo.grossWeight,
    tare_weight: item.selectedInfo.tareWeight,
  }));

  // data for register shipment - get from shipmentRegistrationInfo in store
  const payload = {
    code: `${displayData.value.mainCode}${displayData.value.subCode}`,
    shipper: displayData.value.shipper,
    date: displayData.value.date,
    gross_weight: confirmWeightInfo.value.grossWeight,
    tare_weight: confirmWeightInfo.value.tareWeight,
    setting: {
      display_shipment_weight: settingUser.value.display_shipment_weight,
    },
    ingredient,
    type_diff: reasonInfo.value?.typeDiff || undefined,
    reason_diff: reasonInfo.value?.reasonDiff || undefined,
    inventory_type: displayData.value.inventoryType,
    code_suffix_id: displayData.value.codeSuffixId,
  };
  // save data to register shipment
  return outboundShipmentService.registerOutboundShipment(payload);
};

const printReport = async () => {
  const result = await saveOutboundShipment();
    const isShowQrCode = !result.payload.arrival_date
  const resultPrint = await shippingService.exportShippingDetail({
          id: result.payload.id,
        });
        const dataPrint = makeShippingInfoXML(
          resultPrint.payload.destination_enterprise_name,
          resultPrint.payload.destination_user_name,
          maskCodeString(resultPrint.payload.code?.replaceAll('-', '')),
          FORMAT_NUMBER(resultPrint.payload.shipping_net_weight),
          // map utc to local time
          dayjs(FORMAT_DATE(resultPrint.payload.shipping_date)).toDate(),
          isShowQrCode ? `${resultPrint.payload.qr_code}` : undefined,
          resultPrint.payload.starting_enterprise_name,
          resultPrint.payload.starting_user_name,
          1
        );

        const href = `tmprintassistant://tmprintassistant.epson.com/print?ver=1&data-type=eposprintxml&data=${dataPrint}`;

        const aTag = document.createElement('a');
        aTag.href = href;
        aTag.click();
        toast.access(MESSAGE.MSG_RESISTER_SHIPPING_INFO);
};

// #endregion

// #region functions
// function to control popup confirm total weight
const handleConfirmTotalWeight = () => {
  isShowPopupConfirmTotalWeight.value = true;
};
const handleCancelPopupConfirmTotalWeight = () => {
  isShowPopupConfirmTotalWeight.value = false;
};
const handleConfirmPopupConfirmTotalWeight = payload => {
  confirmWeightInfo.value = {
    ...payload,
  };
  handleCancelPopupConfirmTotalWeight();
};

// function to control popup difference measurement
const handleCancelPopupDifferenceMeasurement = () => {
  isShowPopupDifferenceMeasurement.value = false;
};
const handleConfirmPopupDifferenceMeasurement = payload => {
  reasonInfo.value = {
    ...payload,
  };
  isShowPopupDifferenceMeasurement.value = false;
  isShowPopupDownloadPDF.value = true;
};

const handleClickSubmit = () => {
  // check difference weight
  const netWeightDiffList = [];

  // calc condition to show popup reason for total net weight
  if (confirmWeightInfo.value) {
    const netWeight = confirmWeightInfo.value.grossWeight - confirmWeightInfo.value.tareWeight;
    const netWeightDiff = (1.0 * netWeight) / displayData.value.totalNetWeightSelected;
    // if total net weight is over limit weight or less than limit weight => show popup reason
    const compareNetWeightDiff =
      netWeightDiff > weightAlertThreshold.value / 100 + 1 ||
      netWeightDiff < 1 - weightAlertThreshold.value / 100;

    netWeightDiffList.push(compareNetWeightDiff);
  }

  // calc condition to show popup reason for net weight each item
  displayData.value.inventorySelectedList.forEach(item => {
    const netWeightDiff = item.selectedInfo.netWeight / item.netWeightInventory;
    // if net weight is over limit weight => show popup reason
    const compareNetWeightDiff = netWeightDiff > weightAlertThreshold.value / 100 + 1;
    netWeightDiffList.push(compareNetWeightDiff);
  });

  if (netWeightDiffList.some(diff => diff)) {
    isShowPopupDifferenceMeasurement.value = true;
  } else if (settingUser.value.report_type === REPORT_TYPE_ENUM.PDF_FILE) {
      isShowPopupDownloadPDF.value = true;
  } else if (settingUser.value.report_type === REPORT_TYPE_ENUM.PRINTED_RECEIPT) {
    printReport();
    router.push({ name: 'home' });
  }
};
// #endregion

// #region computed
const inventorySelectedList = computed(() => displayData.value.inventorySelectedList || []);

const checkShipperIsForeign = ref(false);

// #endregion

const mappingData = (shipping = {}, lstPdf = []) => {
  const { shippingInfo } = shipping;
  lstPdf.push(shipping);
  const lst = [];
  if (!shippingInfo) {
    return;
  }
  shippingInfo.forEach(value => {
    mappingData(value, lst);
  });

  if (lst.length) {
    lstPdf.push(lst);
  }
};

const getDataExport = async id => {
  const result = await shippingService.exportShippingDetail({
    id,
  });
  if (!result.payload.foreign_flag) {
    const isShowQrCode = !result.payload.arrival_date;
      dataExport.value = {
        shipping_date: FORMAT_DATE(result.payload.shipping_date),
        code: result.payload.code,
        starting_enterprise: CHECK_ROLE(
          [ROLES_ENUM.NORMAL_USER],
          [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
          [STAFF_TYPE_ENUM.ENTERPRISE],
          result.payload.starting_user
        )
          ? result.payload.starting_user_name
          : `${result.payload.starting_user_name}（${result.payload.starting_enterprise_name}）`,
        destination_enterprise:
          CHECK_ROLE(
            [ROLES_ENUM.NORMAL_USER],
            [ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
            [],
            result.payload.destination_user
          ) ||
          CHECK_ROLE(
            [ROLES_ENUM.NORMAL_USER],
            [ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
            [STAFF_TYPE_ENUM.ENTERPRISE],
            result.payload.destination_user
          )
            ? result.payload.destination_user_name
            : `${result.payload.destination_user_name}（${result.payload.destination_enterprise_name}）`,
        weight: settingUser.value.display_shipment_weight
          ? result.payload.shipping_net_weight
          : undefined,
        qr_code: isShowQrCode ? result.payload.qr_code : undefined,
      };

      nextTick(async () => {
        const pdfFileData = document.getElementById('shipment-pdf-file-data');
        html2pdf()
          .from(pdfFileData)
          .set({
            filename: `出荷情報_${FORMAT_DATE_TIME_CSV()}.pdf`,
            margin: [0.5, 1.25],
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { scale: 1 },
            jsPDF: { unit: 'in', format: 'a4', orientation: 'landscape' },
          })
          .save();
        toast.access(MESSAGE.MSG_DOWNLOAD_SHIPPINGPDF_INFO);
      });
  } else {
    const lstPdf = [];
    mappingData(result.payload, lstPdf);
    lstPdf.forEach(value => {
      if (value.shippingInfo) {
        value.sum = value.shippingInfo.reduce((acc, curr) => +acc + (+Number(curr) || 0), 0);
      }
    });
    exportShipmentPDFProvideData.value = {
      distributionOrigins: [lstPdf],
    };

    nextTick(async () => {
      const pdfFileData = document.getElementById('export-shipment-pdf-file-data');
      html2pdf()
        .from(pdfFileData)
        .set({
          filename: `輸出向け取引記録_${FORMAT_DATE_TIME_CSV()}.pdf`,
          margin: [0.5, 0.3, 0.6, 0.3],
          image: { type: 'jpeg', quality: 0.98 },
          html2canvas: { scale: 1 },
          jsPDF: { unit: 'in', format: 'a3', orientation: 'landscape' },
        })
        .save();
      toast.access(MESSAGE.MSG_DOWNLOAD_EXPORTPDF_INFO);
    });
  }
};

const popupConfirmTextProvideData = {
  isPopup: isShowPopupDownloadPDF,
  titlePopup: '確認',
  caption: '出荷情報PDFをダウンロードしますか？',
  handleCloseModal: async () => {
    isShowPopupDownloadPDF.value = false;
    const result = await saveOutboundShipment();
    if (result.code === 0) {
      toast.access(MESSAGE.MSG_RESISTER_SHIPPING_INFO);
      router.push({ name: 'home' });
    }
  },
  handleAcceptModal: async () => {
    isShowPopupDownloadPDF.value = false;
    const result = await saveOutboundShipment();
    if (result.code === 0) {
      await getDataExport(result.payload.id);
      router.push({ name: 'home' });
    }

    // TODO: download PDF here with checkShipperIsForeign to choice template
  },
};
provide('popupConfirmTextProvideData', popupConfirmTextProvideData);
provide('outBoundShipmentPDFProvideData', dataExport);
provide('exportShipmentPDFProvideData', exportShipmentPDFProvideData);

onMounted(async () => {
  const confirmData = getConfirmData();
  if (confirmData && previousRoute.value.name === 'registerOutboundShipment') {
    displayData.value = confirmData;
    confirmWeightInfo.value = {
      grossWeight: displayData.value.totalNetWeightSelected,
      tareWeight: 0,
    };
    checkShipperIsForeign.value =
      +displayData.value.shipperSelected?.enterprise_type === ENTERPRISE_TYPE_ENUM.FOREIGN;
    if (checkShipperIsForeign.value) {
      popupConfirmTextProvideData.caption = '輸出向け取引記録PDFをダウンロードしますか？';
    }

    const systemSettingResponse = await systemSettingsAdminService.getSystemSettingsForNormalUser();
    weightAlertThreshold.value =
      systemSettingResponse.payload[ADMIN_SYSTEM_SETTING_KEYS_ENUM.WEIGHT_ALERT_THRESHOLD];
  } else {
    router.push({ name: 'registerOutboundShipment' });
  }
});
</script>
