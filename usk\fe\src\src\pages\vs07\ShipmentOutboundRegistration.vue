<template>
  <div class="tw:flex tw:flex-col tw:h-full tw:pb-[20rem] tw:tl:pb-[8rem]">
    <StepCustom v-model="step" class="tw:pb-3" />
    <q-tab-panels v-model="step" animated class="tw:text-[#333333] tw:flex-1">
      <q-tab-panel :name="STEP_ENUM.STEP_1" class="tw:p-4">
        <h2 :class="`tw:text-xs-design tw:tl:font-bold tw:my-3 tw:tl:mt-0`">
          必要項目を入力して「次に進む」ボタンを押してください。
        </h2>
        <form class="tw:grid tw:grid-cols-1 tw:gap-y-10 tw:tl:grid-cols-2 tw:gap-x-12">
          <!-- 漁獲/荷口番号 -->
          <div>
            <BaseLabel label="漁獲/荷口番号" isRequired />
            <div
              class="tw:flex tw:mb-2 tw:text-xl-design tw:gap-2 tw:text-[#333333] tw:items-start"
            >
              <div class="tw:shrink-0">{{ maskMainCode(form.mainCode) }}</div>
              <BaseInput
                v-model="form.subCode"
                autocomplete="nope"
                maxlength="3"
                inputmode="numeric"
                outlined
                input-class="tw:text-xl-design tw:text-[#333333]"
                class="tw:w-full vs tw:mt-0.5"
                :error="!!errorStep1.subCode"
                :error-message="errorStep1.subCode"
                :mask="{
                  mask: /^\d{0,3}$/,
                }"
              >
              </BaseInput>
            </div>
          </div>
          <!-- 出荷先 -->
          <div>
            <BaseLabel label="出荷先" isRequired />
            <div>
              <BasePartnerSelect
                :error-message="errorStep1.shipper"
                :error="!!errorStep1.shipper"
                v-model="form.shipper"
              />
            </div>
          </div>
          <!-- 出荷日 -->
          <div>
            <BaseLabel label="出荷日" isRequired />
            <BaseDatePicker
              v-model="form.date"
              class="tw:flex-1"
              input-class="tw:text-xl-design tw:text-[#333333]"
              :class="{
                'tw:sm:mb-[0.5rem] tw:tl:mb-[1.3rem]': !!errorStep1.date,
              }"
              :error="!!errorStep1.date"
              :error-message="errorStep1.date"
            />
          </div>
        </form>
      </q-tab-panel>
      <q-tab-panel :name="STEP_ENUM.STEP_2" class="tw:p-4">
        <h2 :class="`tw:text-xs-design tw:tl:font-bold tw:my-3 tw:tl:mt-0`">
          出荷する在庫と出荷量を設定し、「確認する」ボタンを押してください。
        </h2>
        <form class="tw:grid tw:grid-cols-1 tw:gap-2">
          <div
            class="tw:flex tw:tl:justify-between tw:tl:items-center tw:flex-col tw:tl:flex-row tw:gap-2"
          >
            <q-checkbox
              class="tw:text-m-design"
              size="5rem"
              color="#004AB9"
              v-model="selectAll"
              label="全出荷対象を選択"
              @update:model-value="handleChangeSelectAll"
            />
            <div
              class="vs flex items-center tw:text-xs-design tw:h-[4.25rem] tw:w-[34rem] tw:tl:w-auto tw:tl:gap-2 tw:justify-between"
              v-if="optionsInventoryType.length > 0"
            >
              <span class="text-xl font-bold">出荷対象</span>
              <BaseSingleSelectInput
                v-model="formSearch.type"
                :input-class="`tw:!text-m-design`"
                class="tw:w-[25rem] tw:tl:w-[20rem] tw:!text-m-design tw:h-[4.25rem]"
                :options="optionsInventoryType"
                :use-input="false"
                :clearable="false"
                emit-value
                map-options
                @update:model-value="getShipmentList"
              />
            </div>
            <SortByDropdownSP
              v-model="sortBySelectedSP"
              class="tw:text-xs-design tw:h-[4.25rem] tw:w-[34rem] tw:tl:w-auto tw:tl:gap-2 tw:justify-between"
            />
          </div>
          <q-table
            grid
            hide-bottom
            card-container-class="tw:flex-col tw:gap-8 tw:tl:gap-6"
            card-class="tw:w-full"
            row-key="id"
            :rows="sortedRows"
          >
            <template v-slot:item="props">
              <div
                class="tw:border-2 tw:rounded-[0.5rem]"
                :class="{
                  'tw:border-[#004AB9]': props.row.selectedInfo.checked,
                  'tw:border-[#CACACA]': !props.row.selectedInfo.checked,
                }"
              >
                <q-card flat>
                  <q-card-section
                    class="tw:cursor-pointer tw:text-[#333333] tw:text-m-design tw:p-0 tw:flex tw:tl:flex-row tw:justify-between tw:flex-col"
                  >
                    <div class="tw:flex tw:items-center">
                      <!-- Checkbox -->
                      <q-checkbox
                        v-model="props.row.selectedInfo.checked"
                        size="5rem"
                        color="primary"
                        @update:model-value="
                          (value, _) => handleChangeSelectedRow(value, props.row)
                        "
                      />
                      <div>
                        <div
                          class="tw:font-bold tw:mb-2 tl:mb-0 tw:block tw:max-w-[80vw] tw:truncate"
                        >
                          {{ props.row.groupName }}
                        </div>
                        <div class="tw:flex-col tw:flex tw:tl:flex-row tw:gap-2 tw:tl:gap-6">
                          <div class="tw:text-m-design">
                            <p class="tw:m-0">
                              最新の入荷日：{{ FORMAT_DATE(props.row.latestArrivalDate) }}
                            </p>
                          </div>
                          <div>
                            <span
                              >総在庫量：{{
                                FORMAT_NUMBER(props.row.netWeightInventory, 2) || 0
                              }}
                              g</span
                            >
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="tw:flex tw:flex-col tw:gap-2 tw:p-2 tw:pr-4 tw:ml-2">
                      <div
                        class="tw:font-bold tw:text-right"
                        :class="{
                          'tw:text-[#CACACA]': !props.row.selectedInfo.checked,
                        }"
                      >
                        {{ mapNetWeightInventoryToDisplay(props.row) }}
                      </div>
                      <BaseButton
                        outline
                        class="tw:rounded-[40px]"
                        :class="`tw:bg-white tw:text-blue-3 tw:text-xs-design`"
                        label="出荷量を変更する"
                        @click="handleClickEditShipping(props.row)"
                      />
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </template>
            <template>
              <div class="tw:w-full tw:text-center tw:text-s-design">データが見つかりません。</div>
            </template>
          </q-table>
        </form>
      </q-tab-panel>
    </q-tab-panels>

    <q-footer
      elevated
      class="tw:bg-white tw:p-4 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
      tw:tl:justify-between tw:flex tw:justify-center tw:mt-4 tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <div
        class="tw:text-[#333333] tw:font-bold tw:gap-2 tw:flex tw:justify-center tw:items-center tw:tl:hidden"
        v-if="step === STEP_ENUM.STEP_2"
      >
        <span class="tw:block tw:text-m-design">出荷量合計</span>
        <span class="tw:block tw:text-xl-design"
          >{{ totalNetWeightSelected ? FORMAT_NUMBER(totalNetWeightSelected, 2) : '000,000.00'
          }}<span class="tw:text-m-design">g</span></span
        >
      </div>
      <BaseButton
        v-if="step === STEP_ENUM.STEP_1"
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
        tw:tl:max-h-[5.43rem] tw:tl:min-h-[5.43rem] tw:h-[5.43rem]`"
        label="出荷登録をやめる"
        @click.prevent="handleBack"
      />
      <BaseButton
        v-else
        outline
        class="tw:rounded-[40px] tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
        tw:tl:w-[18.9rem] tw:tl:max-h-[5.43rem] tw:tl:min-h-[5.43rem] tw:h-[5.43rem] tw:w-full"
        label="前に戻る"
        @click="handleBackStep1"
      />
      <div class="tw:flex tw:items-center tw:gap-5">
        <div
          class="tw:text-[#333333] tw:font-bold tw:gap-2 tw:hidden tw:tl:flex tw:items-center"
          v-if="step === STEP_ENUM.STEP_2"
        >
          <span class="tw:block tw:text-m-design">出荷量合計</span>
          <span class="tw:block tw:text-xl-design"
            >{{ totalNetWeightSelected ? FORMAT_NUMBER(totalNetWeightSelected, 2) : '000,000.00'
            }}<span class="tw:text-m-design">g</span></span
          >
        </div>
        <BaseButton
          outline
          class="tw:rounded-[40px]"
          :class="`tw:bg-[#004AB9] tw:text-white tw:text-m-design tw:tl:font-bold
          tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
          tw:w-full`"
          label="次に進む"
          @click.prevent="handleClickNext"
          :disabled="step === STEP_ENUM.STEP_2 && selectAll === false"
        />
      </div>
    </q-footer>
  </div>

  <PopupEditShipping
    v-model="isShowPopupEditShipping"
    :inventory="inventorySelected"
    @onClickCancel="handleCancelPopupEditShipping"
    @onClickSubmit="handleConfirmPopupEditShipping"
  >
  </PopupEditShipping>
</template>

<script setup>
// #region import
import dayjs from 'boot/dayjs';
import BaseButton from 'components/base/vs/BaseButton.vue';
import useValidate from 'composables/validate';
import { storeToRefs } from 'pinia';
import commonService from 'services/common.service';
import BaseDatePicker from 'src/components/base/vs/BaseDatePicker.vue';
import BaseInput from 'src/components/base/vs/BaseInput.vue';
import BaseLabel from 'src/components/base/vs/BaseLabel.vue';
import BasePartnerSelect from 'src/components/base/vs/BasePartnerSelect.vue';
import BaseSingleSelectInput from 'src/components/base/vs/BaseSingleSelectInput.vue';
import SortByDropdownSP from 'src/components/SortByDropdownSP.vue';
import StepCustom from 'src/components/StepCustom.vue';
import { FORMAT_DATE, FORMAT_NUMBER, GENERATE_CODE_SUFFIX } from 'src/helpers/common';
import {
  INVENTORY_TYPE_ENUM,
  MAX_EXPORT_WEIGHT,
  OPTION_TYPE_ENUM,
  STEP_ENUM,
} from 'src/helpers/constants';
import { registerStep1 } from 'src/schemas/outbound-shipment/outboundShipmentRegister.schema.js';
import outboundShipmentService from 'src/shared/services/outboundShipment.service';
import { useAuthStore } from 'src/stores/auth-store';
import { useConfirmFormStore } from 'src/stores/confirm-form-store';
import { useAppStore } from 'stores/app-store';
import { computed, onMounted, provide, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import toast from 'src/shared/utilities/toast';
import MESSAGE from 'src/helpers/message';

import PopupEditShipping from './components/PopupEditShipping.vue';

// #endregion import

// #region variable
const { setConfirmData, getConfirmData } = useConfirmFormStore();
const { settingUser, previousRoute } = storeToRefs(useAppStore());
const { user } = storeToRefs(useAuthStore());
const router = useRouter();
const { validateData: validateStep1, errors: errorStep1 } = useValidate();
const form = ref({
  mainCode: '0000000' + dayjs().format('YYMMDD'),
  subCode: '',
  shipper: '',
  date: dayjs().format('YYYY/MM/DD'),
});
const optionsPartner = ref([]);
const optionsInventoryType = ref([]);
const step = ref(STEP_ENUM.STEP_1);
const codeSuffixIdRegister = ref(null);
const selectAll = ref(false);
const totalNetWeightSelected = ref(0);
const multiSortConditions = ref([]);
const sortBySelectedSP = ref({
  label: '新着順',
  value: 'latest_arrival_date',
});
const formSearch = ref({
  type: INVENTORY_TYPE_ENUM.DOMESTIC,
});
const inventoryList = ref([]);
const isShowPopupEditShipping = ref(false);
const inventorySelected = ref(null);
// #endregion variable

// #region action
const handleBack = () => {
  router.back();
};

const handleBackStep1 = async () => {
  step.value = STEP_ENUM.STEP_1;

  await reformatCode();
};

const handleClickNext = async () => {
  if (step.value === STEP_ENUM.STEP_1) {
    const validate = validationStep1();
    if (!validate) {
      return;
    }
    step.value = STEP_ENUM.STEP_2;
  } else {
    if (totalNetWeightSelected.value >= MAX_EXPORT_WEIGHT) {
      toast.error(MESSAGE.MSG_SAFE_SHIPPINGWEIGHT_TOTAL_ERROR);
      return;
    }

    await reformatCode();

    const shipperObject = optionsPartner.value.find(item => item.value === form.value.shipper);
    const inventorySelectedList = inventoryList.value.filter(item => item.selectedInfo.checked);
    setConfirmData({
      ...form.value,
      codeSuffixId: codeSuffixIdRegister.value,
      shipperSelected: shipperObject,
      totalNetWeightSelected: totalNetWeightSelected.value,
      inventorySelectedList,
      inventoryType: formSearch.value.type,
    });
    router.push({
      name: 'registerOutboundConfirm',
    });
  }
};

const handleChangeSelectAll = value => {
  selectAll.value = value;
  const newInventoryList = inventoryList.value.map(item => {
    if (!value) {
      item.selectedInfo = {
        netWeight: 0,
        grossWeight: 0,
        tareWeight: 0,
        checked: false,
      };
    } else {
      item.selectedInfo = {
        netWeight: item.netWeightInventory,
        grossWeight: item.netWeightInventory,
        tareWeight: 0,
        checked: value,
      };
    }
    return item;
  });

  inventoryList.value = newInventoryList;

  if (!value) {
    totalNetWeightSelected.value = 0;
  } else {
    totalNetWeightSelected.value = inventoryList.value.reduce(
      (total, item) => total + item.netWeightInventory,
      0
    );
  }
};

const handleChangeSelectedRow = (value, row) => {
  if (value) {
    row.selectedInfo = {
      netWeight: row.netWeightInventory,
      grossWeight: row.netWeightInventory,
      tareWeight: 0,
      checked: true,
    };

    totalNetWeightSelected.value = totalNetWeightSelected.value + row.netWeightInventory;
  } else {
    totalNetWeightSelected.value = totalNetWeightSelected.value - row.selectedInfo.netWeight;

    row.selectedInfo = {
      netWeight: 0,
      grossWeight: 0,
      tareWeight: 0,
      checked: false,
    };
  }

  defineStatusSelectAll();
};

const handleClickEditShipping = row => {
  inventorySelected.value = row;
  isShowPopupEditShipping.value = true;
};

const handleCancelPopupEditShipping = () => {
  isShowPopupEditShipping.value = false;
  inventorySelected.value = null;
};

const handleConfirmPopupEditShipping = payload => {
  const updatedInventoryList = inventoryList.value.map(item => {
    if (item.id === inventorySelected.value.id) {
      item.selectedInfo.checked = true;
      item.selectedInfo.grossWeight = payload.grossWeight;
      item.selectedInfo.tareWeight = payload.tareWeight;
      item.selectedInfo.netWeight = payload.grossWeight - payload.tareWeight;
    }
    return item;
  });
  inventoryList.value = updatedInventoryList;
  totalNetWeightSelected.value = updatedInventoryList.reduce(
    (total, item) => total + item.selectedInfo.netWeight,
    0
  );
  defineStatusSelectAll();
  handleCancelPopupEditShipping();
};
// #endregion

// #region helpers
// define status of select all checkbox
const defineStatusSelectAll = () => {
  const selectedItems = inventoryList.value.filter(item => item.selectedInfo.checked);
  if (selectedItems.length === 0) {
    selectAll.value = false;
  } else if (selectedItems.length === inventoryList.value.length) {
    selectAll.value = true;
  } else {
    selectAll.value = null;
  }
};

// get shipment list from server
const getShipmentList = async () => {
  const inventoryListResponse = await outboundShipmentService.getInventoryListOptions({
    type: formSearch.value.type,
  });

  if (inventoryListResponse) {
    // map data to render in table
    inventoryList.value = inventoryListResponse.payload.items.map(item => ({
      id: item.id,
      groupName: item.group_name,
      netWeightInventory: item.net_weight_inventory,
      latestArrivalDate: FORMAT_DATE(item.latest_arrival_date || ''),
      selectedInfo: {
        checked: false,
        grossWeight: 0,
        tareWeight: 0,
        netWeight: 0,
      },
    }));

    if (inventoryListResponse.payload.inventory_type_options) {
      optionsInventoryType.value = inventoryListResponse.payload.inventory_type_options;
    }

    // set selected all
    selectAll.value = true;
    handleChangeSelectAll(true);
  }
};

const maskMainCode = value => {
  if (value?.length !== 13) {
    return '';
  }
  const part1 = value.slice(0, 7);
  const part2 = value.slice(7, 13);
  return `${part1}-${part2}-`;
};

const mapNetWeightInventoryToDisplay = item => {
  if (item.selectedInfo.checked && item.selectedInfo.netWeight < item.netWeightInventory) {
    return `${FORMAT_NUMBER(item.selectedInfo.netWeight, 2)}g`;
  }
  return '在庫全て';
};

const validationStep1 = () => {
  const payload = {
    mainCode: form.value.mainCode,
    subCode: form.value.subCode,
    shipper: form.value.shipper,
    date: form.value.date,
  };

  return validateStep1(registerStep1, payload);
};

const needGenerateSuffixCode = () => {
  const applyCode = form.value.mainCode.substring(0, 7);
  switch (applyCode) {
    case 'YUNYU**':
      return formSearch.value.type !== INVENTORY_TYPE_ENUM.IMPORTED_EXPORTED;
    case 'JINKOU*':
      return formSearch.value.type !== INVENTORY_TYPE_ENUM.OTHER;
    default:
      return formSearch.value.type !== INVENTORY_TYPE_ENUM.DOMESTIC;
  }
};

const reformatCode = async () => {
  if (needGenerateSuffixCode()) {
    switch (formSearch.value.type) {
      case INVENTORY_TYPE_ENUM.IMPORTED_EXPORTED:
        form.value.mainCode = `YUNYU**${dayjs(form.value.date).format('YYMMDD')}`;
        break;
      case INVENTORY_TYPE_ENUM.OTHER:
        form.value.mainCode = `JINKOU*${dayjs(form.value.date).format('YYMMDD')}`;
        break;
      default:
        form.value.mainCode = `${user.value?.user_code?.slice(0, 7) || ''}${dayjs(
          form.value.date
        ).format('YYMMDD')}`;
        break;
    }

    const codeSuffixResponse = await GENERATE_CODE_SUFFIX(form.value.mainCode);
    codeSuffixIdRegister.value = codeSuffixResponse.id;
    form.value.subCode = codeSuffixResponse.code_suffix;
  }
};
// #endregion

// #region watch
watch(
  () => form.value.date,
  async () => {
    if (dayjs(form.value.date, 'YYYY/MM/DD', true).isValid()) {
      form.value.mainCode =
        form.value.mainCode.substring(0, 7) + dayjs(form.value.date).format('YYMMDD');
      const codeSuffixResponse = await GENERATE_CODE_SUFFIX(form.value.mainCode);
      codeSuffixIdRegister.value = codeSuffixResponse.id;
      form.value.subCode = codeSuffixResponse.code_suffix;
    }
  }
);
// #endregion watch

// #region computed
const sortedRows = computed(() => {
  const rows = [...inventoryList.value];
  return rows.sort((a, b) => {
    for (const { key, order } of multiSortConditions.value) {
      const aVal = a[key] ?? '';
      const bVal = b[key] ?? '';
      // Normalize comparison
      if (aVal < bVal) {
        return order === 'asc' ? -1 : 1;
      }
      if (aVal > bVal) {
        return order === 'asc' ? 1 : -1;
      }
    }

    return 0;
  });
});
// #endregion computed

// #region provide
provide('basePartnerSelectProvideData', { listPartner: optionsPartner });
const sortByDropdownProvideData = {
  sortByOptions: [
    { label: '新着順', value: 'latestArrivalDate' },
    { label: '在庫量順', value: 'netWeightInventory' },
    { label: 'グループ名順', value: 'groupName' },
  ],
  handleClickSortByItem: async option => {
    if (option.value === 'groupName') {
      multiSortConditions.value = [{ key: option.value, order: 'asc' }];
    } else {
      multiSortConditions.value = [{ key: option.value, order: 'desc' }];
    }
  },
};
provide('sortByDropdownProvideData', sortByDropdownProvideData);
// #endregion provide

onMounted(async () => {
  const partnerOptionsResponse = await commonService.getOptions({
    type: OPTION_TYPE_ENUM.USER_SHIPPER,
  });
  optionsPartner.value = partnerOptionsResponse.payload;

  // get data from confirm form store
  const shipmentStoreData = getConfirmData();
  // if shipmentStoreData is not empty, set form values
  if (shipmentStoreData && previousRoute.value.name === 'registerOutboundConfirm') {
    const {
      codeSuffixId,
      shipperName,
      totalNetWeightSelected: totalNetWeightSelectedStore,
      totalNetWeightSelectEdited,
      inventorySelectedList,
      inventoryType,
      ...rest
    } = shipmentStoreData;
    // pass rest of the data to form
    form.value = {
      ...form.value,
      ...rest,
    };
    // set inventoryType
    formSearch.value.type = inventoryType;
    // set codeSuffixId
    codeSuffixIdRegister.value = codeSuffixId;
    // get inventory list and replace values user selected
    await getShipmentList();
    const updatedInventoryList = inventoryList.value.map(item => {
      // check if item was having in inventory selected
      const selectedItem = inventorySelectedList.find(selected => selected.id === item.id);
      if (selectedItem) {
        item.selectedInfo = {
          checked: true,
          grossWeight: selectedItem.selectedInfo.grossWeight,
          tareWeight: selectedItem.selectedInfo.tareWeight,
          netWeight: selectedItem.selectedInfo.netWeight,
        };
      }

      return item;
    });
    inventoryList.value = updatedInventoryList;
    totalNetWeightSelected.value = totalNetWeightSelectedStore;
    defineStatusSelectAll();
  } else {
    // pass information to form
    const mainCode = (user.value?.user_code?.slice(0, 7) || '') + dayjs().format('YYMMDD');
    const codeSuffixResponse = await GENERATE_CODE_SUFFIX(mainCode);
    codeSuffixIdRegister.value = codeSuffixResponse.id;
    form.value.mainCode = mainCode;
    form.value.subCode = codeSuffixResponse.code_suffix;
    form.value.shipper = settingUser.value?.destination_id || '';

    // get inventory list
    await getShipmentList();
  }
});
</script>

<style scoped>
:deep(.q-field--outlined .q-field__control) {
  padding-right: 0;
}

:deep(.q-field__control-container .q-field__suffix) {
  margin-right: 1rem !important;
}
</style>
