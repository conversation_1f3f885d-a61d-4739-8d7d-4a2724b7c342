<template>
  <PopupConfirmText />
  <div class="tw:mb-4 tw:mt-2">
    <q-card class="tw:mb-4 tw:bg-white tw:p-4 tw:min-h-[calc(100vh-15rem)] tw:pb-[10rem]">
      <div class="tw:text-l-design tw:font-[700] tw:mb-5">在庫修正実績</div>
      <div class="tw:border tw:border-[#D2D2D2] tw:rounded-none">
        <div class="tw:flex tw:flex-col tw:divide-y tw:divide-[#D2D2D2]">
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:min-h-[4.25rem] tw:bg-[#E2E3EA] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design"
            >
              グループ名
            </div>
            <div
              class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design"
            >
              {{
                inventoryDetail?.inventory?.group_name ||
                inventoryDetail?.inventory?.fallback_group_name
              }}
            </div>
          </div>

          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:min-h-[4.25rem] tw:bg-[#E2E3EA] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design"
            >
              漁獲/荷口番号：仕入先
            </div>
            <div
              class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design
              tw:max-h-[16rem] tw:overflow-y-auto"
            >
              <div
                v-for="item in inventoryDetail?.inventory.the_origins"
                :key="item.id"
              >
                {{ maskCodeString(item.code) }}：{{
                  `${item.starting_enterprise?.enterprise_name}`
                }}
              </div>
            </div>
          </div>

          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:min-h-[4.25rem] tw:bg-[#E2E3EA] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design"
            >
              修正前の在庫量
            </div>
            <div
              class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design"
            >
              {{ inventoryDetail?.net_weight_inventory }}g
            </div>
          </div>

          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:min-h-[4.25rem] tw:bg-[#E2E3EA] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design"
            >
              修正後の在庫量
            </div>
            <div
              class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design"
            >
              {{ inventoryDetail?.new_net_weight_inventory }}g
            </div>
          </div>

          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:min-h-[4.25rem] tw:bg-[#E2E3EA] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design"
            >
              差異の理由
            </div>
            <div
              class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design"
            >
              {{ reasonDiffWeight }}
            </div>
          </div>

          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:min-h-[4.25rem] tw:bg-[#E2E3EA] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design"
            >
              修正日
            </div>
            <div
              class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design"
            >
              {{ FORMAT_DATE(inventoryDetail?.created_on) }}
            </div>
          </div>
        </div>
      </div>
    </q-card>

    <q-footer elevated class="tw:bg-white tw:p-3
    tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
     tw:items-center tw:flex tw:justify-center tw:tl:justify-between
    tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[18.5rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-[94.5%]`"
        label="在庫管理に戻る"
        @click.prevent="handleBack"
      />
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[18.5rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-[94.5%]`" label="修正を取消" @click.prevent="handleClickUndo" />
    </q-footer>
  </div>

</template>
<script setup>
import {
  onMounted, ref, provide, computed,
} from 'vue';
import { useRouter } from 'vue-router';
import inventoryManagementService from 'services/inventoryManagement.service';
import {
  maskCodeString, FORMAT_DATE, clearHTML, linkify,
} from 'helpers/common';
import PopupConfirmText from 'components/PopupConfirmText.vue';
import MESSAGE from 'helpers/message';
import toast from 'utilities/toast';
import { TYPE_DIFFERENCE_WEIGHT_ENUM } from 'helpers/constants';
import BaseButton from 'components/base/vs/BaseButton.vue';

// #region state
const router = useRouter();
const inventoryDetail = ref(null);
const isPopupConformTextPopup = ref(false);
const allowUndoChange = ref(false);
// #endregion

//  #region actions
const handleBack = () => {
  router.push({ name: 'inventoryEditedList' });
};

const handleClickUndo = () => {
  isPopupConformTextPopup.value = true;
};
// #endregion

// #region computed
const reasonDiffWeight = computed(() => {
  switch (inventoryDetail.value?.type_diff) {
    case TYPE_DIFFERENCE_WEIGHT_ENUM.DEATH:
      return '斃死';
    case TYPE_DIFFERENCE_WEIGHT_ENUM.WEIGHT_ERROR:
      return '計量誤差';
    default:
      return `その他 (${clearHTML(linkify(inventoryDetail.value?.reason_diff || ''))})`;
  }
});
// #endregion

// #region lifecycle hooks
onMounted(async () => {
  const inventoryId = router.currentRoute.value?.params?.id;
  if (!inventoryId) {
    router.push({ name: 'inventoryEditedList' });
  }

  const inventoryDetailResponse = await inventoryManagementService
    .getInventoryEditedDetail(inventoryId);
  if (inventoryDetailResponse.code === 401) {
    return;
  }
  if (inventoryDetailResponse.code === 0) {
    inventoryDetail.value = inventoryDetailResponse.payload.data;
    allowUndoChange.value = inventoryDetailResponse.payload.allow_undo_change;
  } else {
    router.back();
  }
});
// #endregion

// #region provide
const popupConfirmTextProvideData = {
  isPopup: isPopupConformTextPopup,
  titlePopup: '取消確認',
  caption: 'この在庫修正を取り消します。よろしいですか？',
  handleCloseModal: () => {
    isPopupConformTextPopup.value = false;
  },
  handleAcceptModal: async () => {
    isPopupConformTextPopup.value = false;
    const result = await inventoryManagementService.undoChangeInventory(inventoryDetail.value.id);
    if (result.code !== 401) {
      if (result.code === 0) {
        toast.access(MESSAGE.MSG_CANCEL_FIXSTOCK_INFO);
        router.push({ name: 'inventoryEditedList' });
      } else {
        toast.error(result.message);
        router.push({ name: 'inventoryEditedList' });
      }
    }
  },
};
provide('popupConfirmTextProvideData', popupConfirmTextProvideData);
// #endregion
</script>
<style scoped>
:deep(a) {
  color: #007bff !important;
}
</style>
