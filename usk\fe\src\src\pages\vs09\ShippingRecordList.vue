<template>
  <q-div class="tw:flex tw:flex-col tw:h-full tw:pb-[22rem] tw:tl:pb-[8rem]">
    <!-- filter start -->
    <div class="tw:pb-5">
      <ExpansionSearchItem v-model:expanded="isSearchExpanded">
        <SearchForm />
      </ExpansionSearchItem>
      <div
        v-if="!checkConditionIsEmpty(searchQueryConditions)"
        class="tw:mt-4 tw:text-xs-design tw:flex tw:items-center tw:gap-4 tw:flex-wrap"
      >
        <span class="tw:font-bold tw:text-m-design tw:text-[#004AB9]"
          >検索条件</span
        >
        <!-- TODO: fill values form search here -->
      </div>
      <div
      v-if="
        searchConditions.enterpriseName ||
        searchConditions.destination ||
        searchConditions.code ||
        searchConditions.licenseNumber ||
        searchConditions.startDate ||
        searchConditions.endDate ||
        searchConditions.note1 ||
        searchConditions.note2
      "
      class="tw:mt-4 tw:mb-4 tw:text-xs-design tw:flex tw:flex-wrap tw:items-center tw:space-x-2"
    >
      <span class="tw:font-bold tw:text-m-design tw:text-[#004AB9]"
        >検索条件</span
      >
      <span
        v-if="searchConditions.enterpriseName"
        class="tw:text-xs-design tw:ml-[1rem] tw:mt-2 tw:p-2 tw:rounded tw:bg-white tw:truncate tw:max-w-[90vw]"
      >
        {{ searchConditions.enterpriseName }}
      </span>
      <span
        v-if="searchConditions.destination"
        class="tw:text-xs-design tw:ml-[1rem] tw:mt-2 tw:p-2 tw:rounded tw:bg-white tw:truncate tw:max-w-[90vw]"
      >
        {{ searchConditions.destination }}
      </span>
      <span
        v-if="searchConditions.code"
        class="tw:text-xs-design tw:ml-[1rem] tw:mt-2 tw:p-2 tw:rounded tw:bg-white"
      >
        {{ searchConditions.code }}
      </span>
      <span
        v-if="searchConditions.licenseNumber"
        class="tw:text-xs-design tw:ml-[1rem] tw:mt-2 tw:p-2 tw:rounded tw:bg-white tw:truncate tw:max-w-[90vw]"
      >
        {{ searchConditions.licenseNumber }}
      </span>
      <span
        v-if="searchConditions.startDate"
        class="tw:text-xs-design tw:ml-[1rem] tw:mt-2 tw:p-2 tw:rounded tw:bg-white tw:truncate tw:max-w-[90vw]"
      >
        {{ searchConditions.startDate }}
      </span>
      <span
        v-if="searchConditions.endDate"
        class="tw:text-xs-design tw:ml-[1rem] tw:mt-2 tw:p-2 tw:rounded tw:bg-white tw:truncate tw:max-w-[90vw]"
      >
        {{ searchConditions.endDate }}
      </span>
      <span
        v-if="searchConditions.note1"
        class="tw:text-xs-design tw:ml-[1rem] tw:mt-2 tw:p-2 tw:rounded tw:bg-white tw:truncate tw:max-w-[90vw]"
      >
        {{ searchConditions.note1 }}
      </span>
      <span
        v-if="searchConditions.note2"
        class="tw:text-xs-design tw:ml-[1rem] tw:mt-2 tw:p-2 tw:rounded tw:bg-white tw:truncate tw:max-w-[90vw]"
      >
        {{ searchConditions.note2 }}
      </span>
    </div>
    </div>
    <!-- filter end -->
    <q-card class="tw:p-5 tw:flex tw:flex-col tw:h-full">
      <!-- sort btn start -->
      <div
        class="tw:flex tw:tl:flex-row tw:flex-col tw:justify-between tw:items-start tw:border-neutral-500 tw:pt-1"
      >
        <div
          class="tw:flex tw:tl:flex-row tw:flex-col tw:items-start tw:space-y-4"
        >
          <div
            class="tw:flex tw:flex-row tw:items-center tw:space-x-2 tw:h-[4.25rem]"
          >
            <span class="tw:text-xs-design">表示件数</span>
            <PageSizeDropdownSP v-model="pageSize" />
          </div>
          <SortByDropdownSP
            v-model="sortBySelectedSP"
            class="tw:text-xs-design tw:space-x-2 tw:h-[4.25rem] tw:tl:hidden"
          />
        </div>
        <div
          class="tw:tl:justify-end tw:flex tw:justify-center tw:pt-5 tw:tl:pt-0 tw:w-full tw:tl:w-auto"
        >
          <PaginationNotifi />
        </div>
      </div>
      <!-- sort btn end -->
      <!-- start table for tablet -->
      <div class="tw:hidden tw:tl:block">
        <q-table
          class="tw:border tw:border-[#D2D2D2] tw:border-collapse"
          :rows="sortedRows"
          :columns="columns"
          row-key="index"
          hide-pagination
          bordered
          v-model:pagination="paginationComputed"
        >
          <template v-slot:header="props">
            <q-tr
              :props="props"
              class="tw:text-s-design tw:border tw:border-[#D2D2D2]"
              :class="`tw:bg-[#E2E3EA]`"
            >
              <q-th
                @click="handleClickSort(props.cols[0].name)"
                class="tw:text-s-design tw:font-bold tw:text-left tw:border-r tw:border-[#D2D2D2]"
              >
                {{ props.cols[0].label
                }}<q-icon
                  v-if="getSortOrder(props.cols[0].name)"
                  :name="
                    getSortOrder(props.cols[0].name) === 'asc'
                      ? 'arrow_upward'
                      : 'arrow_downward'
                  "
                  size="24px"
                  class="tw:ml-1"
                />
              </q-th>
              <q-th
                @click="handleClickSort(props.cols[1].name)"
                class="tw:text-left tw:font-bold tw:text-s-design tw:border-r tw:border-[#D2D2D2]"
              >
                {{ props.cols[1].label
                }}<q-icon
                  v-if="getSortOrder(props.cols[1].name)"
                  :name="
                    getSortOrder(props.cols[1].name) === 'asc'
                      ? 'arrow_upward'
                      : 'arrow_downward'
                  "
                  size="24px"
                  class="tw:ml-1"
                />
              </q-th>
              <q-th
                class="tw:text-left tw:font-bold tw:text-s-design tw:border-r tw:border-[#D2D2D2] tw:dt:w-[12rem] tw:tl:w-[12rem]"
              >
                {{ props.cols[2].label }}
              </q-th>
              <q-th
                class="tw:text-left tw:font-bold tw:text-s-design tw:border-r tw:border-[#D2D2D2] tw:dt:w-[14rem] tw:tl:w-[14rem]"
              >
                {{ props.cols[3].label }}
              </q-th>
              <q-th
                class="tw:text-left tw:font-bold tw:text-s-design tw:border-r tw:border-[#D2D2D2] tw:dt:w-[5rem] tw:tl:w-[5rem]"
              >
                {{ props.cols[4].label }}
              </q-th>
            </q-tr>
          </template>
          <template v-slot:body="props">
            <q-tr
              class="tw:cursor-pointer tw:w-full"
              :props="props"
            >
              <q-td
                key="destination_user_name"
                :props="props"
                class="tw:text-left tw:text-s-design tw:border-r tw:border-[#D2D2D2]"
              >
                <div
                  class="tw:truncate tw:tl:max-w-[500px] tw tw:underline tw:underline-offset-1 tw:text-[#004AB9]"
                  @click.prevent="handleClickShippingDetail(props.row)"
                >
                  {{ props.row.destination_user_name }}
                </div>
              </q-td>
              <q-td
                key="destination_enterprise_name"
                :props="props"
                class="tw:text-left tw:text-s-design tw:border-r tw:border-[#D2D2D2]"
              >
                <div
                  class="tw:truncate tw:tl:max-w-[330px] tw:dt:max-w-[400px]"
                >
                  {{ calcDestinationName(props.row) }}
                </div>
              </q-td>
              <q-td
                key="shipping_date"
                :props="props"
                class="tw:text-left tw:text-s-design tw:border-r tw:border-[#D2D2D2]"
              >
                <div class="tw:truncate tw:dt:w-[12rem] tw:tl:w-[12rem]">
                  {{ FORMAT_DATE(props.row.shipping_date) }}
                </div>
              </q-td>
              <q-td
                key="shipping_net_weight"
                :props="props"
                class="tw:text-left tw:text-s-design tw:border-r tw:border-[#D2D2D2]"
              >
                <div class="tw:truncate tw:dt:w-[14rem] tw:tl:w-[14rem]">
                  {{ FORMAT_NUMBER(props.row.shipping_net_weight) }} g
                </div>
              </q-td>
              <q-td key="is_staff" :props="props" class="tw:text-center">
                <div class="tw:truncate">
                  <q-icon
                    name="person_outline"
                    v-if="
                      CHECK_ROLE(
                        [ROLES_ENUM.NORMAL_USER],
                        [
                          ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
                          ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
                        ],
                        [STAFF_TYPE_ENUM.ENTERPRISE],
                        user
                      ) &&
                      !CHECK_ROLE(
                        [ROLES_ENUM.NORMAL_USER],
                        [
                          ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
                          ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
                        ],
                        [STAFF_TYPE_ENUM.ENTERPRISE],
                        props.row.starting_user
                      )
                    "
                    size="2rem"
                  />
                </div>
              </q-td>
            </q-tr>
          </template>
          <template v-slot:no-data>
            <div class="tw:w-full tw:text-center tw:text-s-design">
              データが見つかりません。
            </div>
          </template>
        </q-table>
      </div>
      <!-- start table for smartphone -->
      <div class="tw:tl:hidden tw:block">
        <q-table
          grid
          card-container-class="tw:flex-col tw:gap-5 tw:mt-4"
          card-class="tw:w-full"
          :rows="sortedRows"
          row-key="index"
          hide-pagination
          hide-header
          :columns="columns"
          v-model:pagination="paginationComputed"
        >
          <template v-slot:item="props">
            <div class="tw:w-full" @click.prevent="handleClickShippingDetail(props.row)">
              <q-card flat bordered>
                <q-card-section
                  class="tw:text-left tw:cursor-pointer tw:text-s-design tw:flex tw:justify-between tw:items-center tw:gap-2"
                >
                  <div>
                    <strong class="tw:break-all tw:text-[#004AB9]">
                      {{ calcDestinationName(props.row) }}</strong
                    >
                    <br />
                    <div class="tw:flex tw:flex-col tw:gap-2 tw:mt-2">
                      <span>
                        {{ props.cols[1].label }}：
                        {{ FORMAT_DATE(props.row.shipping_date) }}
                      </span>
                      <span> {{ props.cols[2].label }}： </span>
                      <span>
                        {{ maskCodeString(props.row.code) }}
                      </span>
                      <span>
                        {{ props.cols[3].label }}：
                        {{ FORMAT_NUMBER(props.row.shipping_net_weight) }}g
                      </span>
                    </div>
                  </div>
                  <q-icon name="person" size="3rem" />
                </q-card-section>
              </q-card>
            </div>
          </template>
          <template v-slot:no-data="">
            <div
              class="tw:w-full tw:text-center tw:text-s-design"
            >
              データが見つかりません。
            </div>
          </template>
        </q-table>
      </div>
      <!-- table end -->
      <div
        class="tw:tl:justify-end tw:flex tw:justify-center tw:pt-5 tw:tl:pt-0 tw:w-full tw:tl:w-auto tw:mt-4"
      >
        <PaginationNotifi />
      </div>
    </q-card>
    <div class="tw:relative tw:h-0 tw:overflow-hidden">
      <ShippingListPdf />
    </div>

    <q-footer
      elevated
      class="tw:bg-white tw:p-4 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
      tw:tl:justify-between tw:flex tw:justify-center tw:mt-4 tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
        tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
        tw:w-full`"
        label="トップに戻る"
        @click="router.push({ name: 'home' })"
      />

      <div
        class="tw:flex tw:tl:justify-between tw:justify-center tw:tl:flex-row tw:flex-col tw:gap-4 tw:tl:gap-5"
      >
        <!-- TODO: Implement download functionality -->
        <BaseButton
          outline
          class="tw:rounded-[40px]"
          :class="`tw:bg-[#004AB9] tw:text-white tw:text-m-design tw:tl:font-bold
          tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem] tw:tl:w-[30.25rem]`"
          label="実績をPDFでダウンロード"
          @click.prevent="exportPdfHandler"
        />
        <BaseButton
          outline
          class="tw:rounded-[40px]"
          :class="`tw:bg-[#004AB9] tw:text-white tw:text-m-design tw:tl:font-bold
          tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem] tw:tl:w-[30rem]`"
          label="実績をCSVでダウンロード"
          @click.prevent="exportCsvHandler"
        />
      </div>
    </q-footer>
  </q-div>
</template>

<script setup>
// import
import ExpansionSearchItem from 'components/ExpansionSearchItem.vue';
import ShippingListPdf from 'components/pdf/ShippingListPdf.vue';
import { useLocalStorage } from 'composables/localstorage';
import useValidate from 'composables/validate';
import {
  exportCSV,
  FORMAT_DATE,
  FORMAT_DATE_TIME_CSV,
  FORMAT_NUMBER,
  maskCodeString,
} from 'helpers/common';
import MESSAGE from 'helpers/message';
import html2pdf from 'html2pdf.js';
import { isEqual, orderBy } from 'lodash';
import { storeToRefs } from 'pinia';
import searchAllShippingList from 'schemas/searchAllShippingList';
import shippingService from 'services/shipping.service';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import PageSizeDropdownSP from 'src/components/PageSizeDropdownSP.vue';
import PaginationNotifi from 'src/components/PaginationNotifi.vue';
import SortByDropdownSP from 'src/components/SortByDropdownSP.vue';
import {
  ROLES_ENUM,
  ENTERPRISE_TYPE_ENUM,
  STAFF_TYPE_ENUM,
} from 'src/helpers/constants';
import { useAuthStore } from 'src/stores/auth-store';
import { useAppStore } from 'stores/app-store';
import toast from 'utilities/toast';
import { computed, nextTick, onMounted, provide, ref, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { CHECK_ROLE } from 'src/helpers/common';

import SearchForm from './components/SearchForm.vue';

// const begin
const router = useRouter();
const { setLoadingManual, setLoading } = useAppStore();
const { user } = storeToRefs(useAuthStore());
const { errors, validateData } = useValidate();
const { listPageQueryParams } = useLocalStorage();
const isSearchExpanded = ref(false);
const sortBySelectedSP = ref('');
const route = useRoute();
// because quasar does not support mutli sort, we use this variable to manage sort conditions
const multiSortConditions = ref([]);
const columns = [
  {
    name: 'destination_user_name',
    label: '出荷先(事業者名)',
    field: 'destination_user_name',
    sortable: true,
  },
  {
    name: 'destination_enterprise_name',
    label: '出荷先(届出事業者名)',
    field: 'destination_enterprise_name',
    sortable: true,
  },
  {
    name: 'shipping_date',
    label: '出荷日',
    field: 'shipping_date',
    sortable: true,
  },
  {
    name: 'shipping_net_weight',
    label: '出荷量',
    field: 'shipping_net_weight',
    sortable: true,
  },
  {
    name: 'is_staff',
    label: '',
    field: 'is_staff',
    sortable: true,
  },
];
// default pagination for table
const pagination = ref({
  sortBy: 'id',
  descending: false,
  page: listPageQueryParams.value.shippingList?.page || 1,
  rowsPerPage: listPageQueryParams.value.shippingList?.rowsPerPage || 10,
  limit: listPageQueryParams.value.shippingList?.limit || 10,
});
const totalPage = ref(1);
// page size default is 10
const pageSize = ref(router.currentRoute.value.query.limit || 10);
// page index default is 1
const pageIndex = ref(listPageQueryParams.value?.shippingList?.page || 1);
const shippingListData = ref([]);
const paginationComputed = computed(() => ({
  page: pagination.value.page,
  rowsPerPage: pagination.value.limit,
}));
const searchConditions = computed(() => {
  const query = route.query;
  return {
    enterpriseName: query.enterpriseName || '',
    destination: query.destination || '',
    code: query.code || '',
    licenseNumber: query.licenseNumber || '',
    startDate: query.startDate || '',
    endDate: query.endDate || '',
    note1: query.note1 || '',
    note2: query.note2 || '',
  };
});
const searchForm = ref({
  enterpriseName: '',
  destination: '',
  code: '',
  licenseNumber: '',
  startDate: '',
  endDate: '',
  note1: '',
  note2: '',
});
const searchFormData = ref({
  enterpriseName: '',
  destination: '',
  code: '',
  licenseNumber: '',
  startDate: '',
  endDate: '',
  note1: '',
  note2: '',
});
const dataExport = ref([]);
const dataExportPdf = ref([]);
const isDownload = ref(false);
const flagDownload = ref(false);
const hasDiffPageIndex = ref(false);
const sortBySelectedLabel = ref('新着順');
const isFirstLoad = ref(true);

// #region function
const handleClickShippingDetail = async row => {
  await router.push({
    name: 'shippingDetail',
    params: {
      id: row.id,
    },
  });
};

const getData = async () => {
  const searchCondition = {
    ...pagination.value,
    ...formatSearchFormToQuery(searchFormData.value),
    descending: undefined,
  };

  const result = await shippingService.getShippingList(searchCondition);
  shippingListData.value = result.payload?.items ?? [];
  if (window.innerWidth < 960){
      let key;
      const order = 'asc';
      switch (router.currentRoute.value.query.sortBy) {
        case 'id':
          key = 'id';
          break;
        case 'shipping_net_weight':
          key = 'shipping_net_weight';
          break;
        case 'enterprise_name':
          key = 'destination_enterprise_name';
          break;
        case 'name':
          key = 'destination_user_name';
          break;
        default:
          break;
      }

  shippingListData.value = orderBy(shippingListData.value
  ,[key]
  ,[order] );
}
  // TODO: map data to match the table structure for sorting and displaying
  shippingListData.value = result.payload.items.map(item => ({
    ...item,
    shipping_date: FORMAT_DATE(item.shipping_date),
  }));
  isDownload.value =
    (searchFormData.value.startDate ||
      searchFormData.value.endDate ||
      searchFormData.value.note1 ||
      searchFormData.value.note2 ||
      searchFormData.value.destination ||
      searchFormData.value.licenseNumber) &&
    result.payload.total_item > 0;
  totalPage.value = Math.ceil(
    (result.payload.total_item ?? 1) / pagination.value.limit
  );
  if (result.payload.page !== pageIndex.value) {
    hasDiffPageIndex.value = true;
    router.replace({
      query: {
        ...pagination.value,
        descending: undefined,
        page: result.payload.page,
        ...formatSearchFormToQuery(searchForm.value),
      },
    });
  }
};

const getDataExport = async (isCsv = true) => {
  flagDownload.value = false;
  const searchCondition = {
    destination: searchFormData.value.destination || undefined,
    licenseNumber: searchFormData.value.licenseNumber,
    startDate: searchFormData.value.startDate || undefined,
    endDate: searchFormData.value.endDate || undefined,
    note1: searchFormData.value.note1,
    note2: searchFormData.value.note2,
    enterpriseName: searchFormData.value.enterpriseName,
    code: searchFormData.value.code,
    sortBy: pagination.value.sortBy,
  };
  const result = await shippingService.exportShippingList(searchCondition);
  if (result.code !== 0) {
    return;
  }
  flagDownload.value = true;
  dataExport.value = result.payload.data.map(item => ({
    shipping_date: FORMAT_DATE(item.shipping_date),
    name: `${item.destination_user_name}${
      !(
        item.destination_user.enterprise_type ===
          ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
        item.destination_user.staff_type === STAFF_TYPE_ENUM.STAFF
      ) && isCsv
        ? '※'
        : ''
    }`,
    license_number: item.destination_license_number,
    code: maskCodeString(item.code),
    enterprise_name:
      !item.destination_user_name ||
      CHECK_ROLE(
        [ROLES_ENUM.NORMAL_USER],
        [ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
        [STAFF_TYPE_ENUM.ENTERPRISE],
        item.destination_user
      ) ||
      CHECK_ROLE(
        [ROLES_ENUM.NORMAL_USER],
        [ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        [],
        item.destination_user
      )
        ? item.destination_enterprise_name
        : `${item.destination_user.name} (${item.destination_enterprise_name})`,
    shipping_net_weight: item.shipping_net_weight,
    mark_staff: !(
      item.destination_user.enterprise_type ===
        ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
      item.destination_user.staff_type === STAFF_TYPE_ENUM.STAFF
    )
      ? '※'
      : '',
    catch_code: item.shipping_id_list ? `"${item.shipping_id_list
      .map(value => maskCodeString(value?.code || '')).join(',')}"` : '',
  }));
  dataExportPdf.value = dataExport.value;
};

const handleClickSort = key => {
  const idx = multiSortConditions.value.findIndex(i => i.key === key);
  if (idx >= 0) {
    const current = multiSortConditions.value[idx];

    if (current.order === 'asc') {
      current.order = 'desc';
      multiSortConditions.value.splice(idx, 1);
      multiSortConditions.value.unshift(current);
    } else if (current.order === 'desc') {
      multiSortConditions.value.splice(idx, 1);
    }
  } else {
    multiSortConditions.value.unshift({ key, order: 'asc' });
  }

  router.push({
    query: {
      ...pagination.value,
      descending: undefined,
      ...formatSearchFormToQuery(searchFormData.value),
    },
  });
};
// #region helpers functions
const getSortOrder = key => {
  const condition = multiSortConditions.value.find(i => i.key === key);
  return condition?.order;
};

// calc destination name for render table
const calcDestinationName = item => {
  // if destination is apply user who has staff type is enterprise
  if (
    CHECK_ROLE(
      [ROLES_ENUM.NORMAL_USER],
      [
        ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
        ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
      ],
      [STAFF_TYPE_ENUM.ENTERPRISE],
      item.destination_user
    ) ||
    CHECK_ROLE(
      [ROLES_ENUM.NORMAL_USER],
      [ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
      [],
      item.destination_user
    )
  ) {
    return item.destination_enterprise_name;
  }

  return item.destination_user_name
    ? `${item.destination_user_name}(${item.destination_enterprise_name})`
    : item.destination_enterprise_name;
};

const formatSearchFormToQuery = form => ({
  destination: form.destination || undefined,
  licenseNumber: form.licenseNumber || undefined,
  startDate: form.startDate || undefined,
  endDate: form.endDate || undefined,
  note1: form.note1 || undefined,
  note2: form.note2 || undefined,
  enterpriseName: form.enterpriseName || undefined,
  code: form.code || undefined,
});

const checkConditionIsEmpty = condition =>
  Object.values(condition).every(
    value => value === '' || value === undefined || value === null
  );

// TODO: export csv here
const exportCsvHandler = async () => {
  // download csv
  await getDataExport();
  if (!flagDownload.value) {
    return;
  }
  exportCSV(
    dataExport.value,
    [
      'shipping_date',
      'name',
      'license_number',
      'code',
      'catch_code',
      'enterprise_name',
      'shipping_net_weight',
      'reason_diff',
    ],
    [
      '出荷日',
      '出荷者※事業者',
      '許可番号',
      '漁獲番号/荷口番号',
      'この番号に紐づく漁獲/荷口番号',
      '出荷先(届出事業者)',
      '出荷量[g]',
      '差異の理由',
    ],
    `シラスウナギ出荷実績_${FORMAT_DATE_TIME_CSV()}`
  );
};

const exportPdfHandler = async () => {
  // download pdf
  await getDataExport(false);
  if (!flagDownload.value) {
    return;
  }
  nextTick(async () => {
    setLoadingManual(true);
    setLoading(true);
    const elementBody = document.getElementById('large-body-pdf');
    const rows = Array.from(elementBody.children);

    const worker = html2pdf().set({
      filename: `シラスウナギ出荷実績_${FORMAT_DATE_TIME_CSV()}.pdf`,
      margin: [0.5, 0.1, 0.6, 0.1],
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: { scale: 1 },
      jsPDF: { unit: 'in', format: 'a4', orientation: 'landscape' },
    });

    const processBatches = async () => {
      for (let i = 0; i < rows.length; ) {
        let batchHeight = 0;
        let rowIndex = i;
        const batchElement = document.createElement('div');
        while (rowIndex < rows.length) {
          const row = rows[rowIndex];
          const rowHeight = row.getBoundingClientRect().height;
          if (batchHeight + rowHeight > 31000) {
            break;
          }
          batchHeight += rowHeight;
          rowIndex += 1;
          batchElement.appendChild(row.cloneNode(true));
        }

        const batchSize = rowIndex - i;
        i += batchSize;

        await worker
          .from(batchElement)
          .toContainer()
          .toCanvas()
          .toPdf()
          .get('pdf')
          .then(pdf => {
            if (i + batchSize < rows.length) {
              pdf.addPage();
            }
          });

        await worker.get('canvas').then(canvas => {
          // reset canvas size
          canvas.height = 0;
          canvas.width = 0;
          canvas.style.height = '0px';
          canvas.style.width = '0px';
        });
      }

      await worker.save();
      setLoading(false);
      setLoadingManual(false);
      toast.access(MESSAGE.MSG_DOWNLOAD_SHIPPINGPDF_INFO);
    };

    await processBatches();
  });
};
// #endregion

// #region computed
const sortedRows = computed(() => {
  const rows = [...shippingListData.value];
  return rows.sort((a, b) => {
    for (const { key, order } of multiSortConditions.value) {
      const aVal = a[key] ?? '';
      const bVal = b[key] ?? '';
      // Normalize comparison
      if (aVal < bVal) {
        return order === 'asc' ? -1 : 1;
      }
      if (aVal > bVal) {
        return order === 'asc' ? 1 : -1;
      }
    }

    return 0;
  });
});

const searchQueryConditions = computed(() => ({
  destination: searchFormData.value.destination || undefined,
  licenseNumber: searchFormData.value.licenseNumber || undefined,
  startDate: searchFormData.value.startDate || undefined,
  endDate: searchFormData.value.endDate || undefined,
  note1: searchFormData.value.note1 || undefined,
  note2: searchFormData.value.note2 || undefined,
  code: searchFormData.value.code || undefined,
  enterpriseName: searchFormData.value.enterpriseName || undefined,
}));
// #endregion

// #region watchers
watch(pageSize, async () => {
  pagination.value.rowsPerPage = +pageSize.value;
  totalPage.value = Math.ceil(shippingListData.value.length / +pageSize.value);
  listPageQueryParams.value.shippingList.rowsPerPage = +pageSize.value;
  pagination.value.limit = pageSize.value;
  const query = {
    ...pagination.value,
    ...formatSearchFormToQuery(searchFormData.value),
  };

  if (!isFirstLoad.value){
    query.page = 1;
  }
  router.push({
    query: {
      ...query,
    },
  });
});

watch(
  () =>
    router.currentRoute.value.query.sortBy +
    router.currentRoute.value.query.enterpriseName +
    router.currentRoute.value.query.destination +
    router.currentRoute.value.query.code +
    router.currentRoute.value.query.licenseNumber +
    router.currentRoute.value.query.startDate +
    router.currentRoute.value.query.endDate +
    router.currentRoute.value.query.note1 +
    router.currentRoute.value.query.page +
    router.currentRoute.value.query.limit +
    router.currentRoute.value.query.rowsPerPage +
    router.currentRoute.value.query.note2,
  async () => {
    if (router.currentRoute.value.query.limit) {
      listPageQueryParams.value.shippingList.limit =
        +router.currentRoute.value.query.limit;
    }

    if (router.currentRoute.value.query.page) {
      listPageQueryParams.value.shippingList.page =
        +router.currentRoute.value.query.page;
    }

    if (router.currentRoute.value.query.rowsPerPage) {
      listPageQueryParams.value.shippingList.rowsPerPage =
      +router.currentRoute.value.query.rowsPerPage;
    }

    pageSize.value = +router.currentRoute.value.query.limit;

    pageIndex.value = +router.currentRoute.value.query.page;

    sortBySelectedLabel.value = router.currentRoute.value.query.sortBy
      ? sortByDropdownProvideData.sortByOptions.find(
          item => item.value === router.currentRoute.value.query.sortBy
        )?.label
      : '新着順';

    pagination.value = {
      ...pagination.value,
      page: router.currentRoute.value.query.page,
      limit: router.currentRoute.value.query.limit,
      sortBy: router.currentRoute.value.query.sortBy,
      rowsPerPage: router.currentRoute.value.query.rowsPerPage,
      descending: router.currentRoute.value.query.descending === 'true',
    };
    searchForm.value = {
      enterpriseName: router.currentRoute.value.query.enterpriseName || '',
      destination: router.currentRoute.value.query.destination || '',
      code: router.currentRoute.value.query.code || '',
      licenseNumber: router.currentRoute.value.query.licenseNumber || '',
      startDate: router.currentRoute.value.query.startDate || '',
      endDate: router.currentRoute.value.query.endDate || '',
      note1: router.currentRoute.value.query.note1 || '',
      note2: router.currentRoute.value.query.note2 || '',
    };
    searchFormData.value = {
      ...searchForm.value,
    };

    listPageQueryParams.value.shippingList = {
      ...listPageQueryParams.value.shippingList,
      ...searchFormData.value,
      sortBy: router.currentRoute.value.query.sortBy,
    };

    if (window.innerWidth < 960){
      let key;
      const order = 'asc';
      switch (router.currentRoute.value.query.sortBy) {
        case 'id':
          key = 'id';
          break;
        case 'shipping_net_weight':
          key = 'shipping_net_weight';
          break;
        case 'enterprise_name':
          key = 'destination_enterprise_name';
          break;
        case 'name':
          key = 'destination_user_name';
          break;
        default:
          break;
      }

      shippingListData.value = orderBy(shippingListData.value
      ,[key]
      ,[order] );
    }

    if (isFirstLoad.value) {
      await getData();
      isFirstLoad.value = false;
    }

  }
);

// #endregion

provide('shippingListDataExport', dataExport);
provide('shippingListDataExportPdf', dataExportPdf);
provide('formDataExport', searchFormData);

const expansionSearchProvideData = {
  handleClear() {
    searchForm.value = {
      enterpriseName: '',
      destination: '',
      code: '',
      licenseNumber: '',
      startDate: '',
      endDate: '',
      note1: '',
      note2: '',
    };
    errors.value = {};
  },
  async handleSearch() {
    if (isEqual({ ...searchForm.value }, { ...searchFormData.value })) {
      await getData();
      this.isClose = true;
    } else {
      const payload = {
        ...searchForm.value,
        code: searchForm.value.code || undefined,
        licenseNumber: searchForm.value.licenseNumber || undefined,
        note1: searchForm.value.note1 || undefined,
        note2: searchForm.value.note2 || undefined,
      };

      const validate = validateData(searchAllShippingList, {
        ...payload,
      });

      isFirstLoad.value = true;
      if (validate) {
        pageIndex.value = 1;
        await router.push({
          query: {
            ...pagination.value,
            descending: undefined,
            page: 1,
            ...formatSearchFormToQuery(searchForm.value),
          },
        });
        this.isClose = true;
      } else {
        this.isClose = false;
      }
    }
  },
  isClose: false,
  title: '出荷実績をさがす',
};
provide('expansionSearchProvideData', expansionSearchProvideData);

const searchFormProvideData = {
  form: searchForm,
  errors,
};
provide('searchFormProvideData', searchFormProvideData);

const sortByDropdownProvideData = {
  sortByOptions: [
    { label: '新着順', value: 'id' },
    { label: '出荷量順', value: 'shipping_net_weight' },
    { label: '出荷先(届出事業者名)順', value: 'enterprise_name' },
    { label: '出荷先(事業者名)順', value: 'name' },
  ],
  sortBySelectedLabel,
  handleClickSortByItem: async option => {
    sortBySelectedLabel.value = option.label;
    pagination.value.sortBy = option.value;
    await router.push({
      query: {
        ...pagination.value,
        descending: undefined,
        page: 1,
        ...formatSearchFormToQuery(searchFormData.value),
      },
    });
  },
};
provide('sortByDropdownProvideData', sortByDropdownProvideData);

const paginationItemProvideData = {
  pageIndex,
  totalPage,
  onChangedPageIndex: async value => {
    pagination.value = {
      ...pagination.value,
      page: value,
    };
    await router.push({
      query: {
        ...pagination.value,
        descending: undefined,
        ...formatSearchFormToQuery(searchFormData.value),
      },
    });
  },
};
provide('paginationItemProvideData', paginationItemProvideData);

onMounted(async () => {
  isSearchExpanded.value = false;
  if (!listPageQueryParams.value.shippingList) {
    listPageQueryParams.value.shippingList = {};
  }
  if (!router.currentRoute.value?.query?.page) {
    isFirstLoad.value = true;
    router.replace({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
        descending: undefined,
      },
    });
  } else {
    pageSize.value = +router.currentRoute.value.query.limit;
    pageIndex.value = +router.currentRoute.value.query.page;
    searchForm.value = searchQueryConditions.value;
    searchFormData.value = searchQueryConditions.value;
    sortBySelectedLabel.value = router.currentRoute.value.query.sortBy
      ? sortByDropdownProvideData.sortByOptions.find(
          item => item.value === router.currentRoute.value.query.sortBy
        )?.label
      : '新着順';
    pagination.value = {
      ...pagination.value,
      page: router.currentRoute.value.query.page,
      limit: router.currentRoute.value.query.limit,
      sortBy: router.currentRoute.value.query.sortBy,
      descending: router.currentRoute.value.query.descending === 'true',
    };

    searchForm.value = {
      enterpriseName: router.currentRoute.value.query.enterpriseName,
      destination: router.currentRoute.value.query.destination,
      code: router.currentRoute.value.query.code,
      licenseNumber: router.currentRoute.value.query.licenseNumber,
      startDate: router.currentRoute.value.query.startDate,
      endDate: router.currentRoute.value.query.endDate,
      note1: router.currentRoute.value.query.note1,
      note2: router.currentRoute.value.query.note2,
    };

    searchFormData.value = {
      enterpriseName: router.currentRoute.value.query.enterpriseName,
      destination: router.currentRoute.value.query.destination,
      code: router.currentRoute.value.query.code,
      licenseNumber: router.currentRoute.value.query.licenseNumber,
      startDate: router.currentRoute.value.query.startDate,
      endDate: router.currentRoute.value.query.endDate,
      note1: router.currentRoute.value.query.note1,
      note2: router.currentRoute.value.query.note2,
    };
    await getData();
    isFirstLoad.value = false;
  }
});
</script>
<style scoped></style>
